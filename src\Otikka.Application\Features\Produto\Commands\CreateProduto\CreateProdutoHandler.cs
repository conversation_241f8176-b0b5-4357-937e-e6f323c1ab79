using FluentValidation;
using Microsoft.Extensions.Logging;
using Otikka.Application.Contracts.Persistence;
using FluentResults;
using Gestao.Dominio.Repositorios;
using Otikka.Domain.Entities.ProdutoModulo.EstoqueModulo;
using Otikka.Domain.Enums;
using Otikka.Domain.Entities.ProdutoModulo;

namespace Otikka.Application.Features.Produto.Commands.CreateProduto;

public record CreateProdutoHandler(
     IProdutoRepositorio produtoRepositorio,
     IMovimentacaoEstoqueRepositorio movimentacaoEstoqueRepositorio,
     IValidator<CreateProduto> validator,
     ILogger<CreateProdutoHandler> logger)
{
    public async Task<Result<Otikka.Domain.Entities.ProdutoModulo.Produto>> Handle(CreateProduto req)
    {
        logger.LogInformation("Iniciando cadastro de produto: {InstanciaNome} - Código: {Codigo}", req.Nome, req.Codigo);

        // Validar o comando antes de processar
        var validationResult = await validator.ValidateAsync(req, options => options.IncludeRuleSets("ProdutoValidacao"));
        if (!validationResult.IsValid)
        {
            var errors = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            logger.LogWarning("Falha na validação do produto {InstanciaNome}: {Errors}", req.Nome, string.Join("; ", errors));
            return Result.Fail(string.Join("; ", errors));
        }

        try
        {
            // Validação de código de referência único
            var produtoExistente = await produtoRepositorio.ObterPorCodigo(req.Codigo);
            if (produtoExistente != null)
            {
                logger.LogWarning("Já existe um produto cadastrado com o código de referência {Codigo}", req.Codigo);
                return Result.Fail($"Já existe um produto cadastrado com o código de referência '{req.Codigo}'");
            }

            // Define data de criação
            req.DataCriacao = DateTimeOffset.Now;

            // Define o EstoqueTotal baseado na quantidade de estoque corrente
            req.EstoqueTotal = req.QuantidadeEstoqueCorrente;

            // Cadastra o produto
            await produtoRepositorio.Cadastrar(req);
            logger.LogInformation("Produto {InstanciaNome} cadastrado com sucesso - ID: {Id}", req.Nome, req.Id);

            // Criar MovimentacaoEstoque do tipo Balanço para o estoque inicial
            if (req.QuantidadeEstoqueCorrente > 0)
            {
                var movimentacaoBalanco = new Domain.Entities.ProdutoModulo.EstoqueModulo.MovimentacaoEstoque
                {
                    Id = Guid.NewGuid(),
                    ProdutoId = req.Id,
                    Quantidade = req.QuantidadeEstoqueCorrente,
                    CustoUnitario = req.CustoUnitario ?? 0, // Usa o custo unitário informado ou zero
                    TipoMovimentacao = TipoMovimentacaoEstoque.Balanco,
                    Descricao = "Balanço inicial - Cadastro do produto",
                    DataMovimentacao = DateTime.Now,
                    CriadoPorId = req.CriadoPorId,
                    EmpresaId = req.EmpresaId,
                    DataCriacao = DateTimeOffset.Now
                };

                // Usar o repositório para cadastrar a movimentação
                // Nota: Como é um balanço inicial, o estoque corrente já foi definido no cadastro do produto
                // Esta movimentação é apenas para registro histórico
                await movimentacaoEstoqueRepositorio.CadastrarSemAtualizarEstoque(movimentacaoBalanco);
                
                logger.LogInformation("MovimentacaoEstoque de balanço criada para produto {ProdutoNome} - Quantidade: {Quantidade}, Custo Unitário: {CustoUnitario}", req.Nome, req.QuantidadeEstoqueCorrente, req.CustoUnitario ?? 0);
            }

            return Result.Ok((Domain.Entities.ProdutoModulo.Produto)req);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Erro ao cadastrar produto {InstanciaNome}: {Message}", req.Nome, ex.Message);
            return Result.Fail($"Erro ao cadastrar produto: {ex.Message}");
        }
    }
}