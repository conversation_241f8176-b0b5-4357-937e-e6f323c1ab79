@using Otikka.Application.Models
@using Otikka.Application.Models.Requests
@using Otikka.Application.Features.Compra.Queries.ListCompras
@using Otikka.Application.Features.Compra.Commands.DeleteCompra
@using Otikka.Application.Features.Pessoa.Queries.ListFornecedores
@using Otikka.Domain.Entities.CompraModule
@using FluentResults
@using Wolverine
@attribute [Route(Application.Routes.CompraListar)]

@inherits PageBase

<PageTitle>Compras</PageTitle>


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">Compras</h4>
                <div class="flex-shrink-0">
                    <a href="@Application.Routes.CompraCadastrar" class="btn btn-primary btn-sm material-shadow-none">
                        <i class="ri-file-list-3-line align-middle"></i> Cadastrar
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="col-lg-6">
                    <div class="flex-shrink-0">
                        <div class="hstack text-nowrap gap-2">
                            <input @bind="PaginationParams.SearchWord" @onkeypress="@(async (e) => { if (e.Key == "Enter") await OnSearch(); })"
                                   class="form-control" placeholder="Buscar..." />
                            <button @onclick="OnSearch" class="btn btn-primary">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-nowrap">
                        <thead>
                            <tr>
                                <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("datacompra")'>
                                    Data
                                    @if (PaginationParams.SortColumn == "datacompra")
                                    {
                                        <i class="@(PaginationParams.Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line")"></i>
                                    }
                                </th>
                                <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("fornecedor")'>
                                    Fornecedor
                                    @if (PaginationParams.SortColumn == "fornecedor")
                                    {
                                        <i class="@(PaginationParams.Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line")"></i>
                                    }
                                </th>
                                <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("nfe")'>
                                    NFe
                                    @if (PaginationParams.SortColumn == "nfe")
                                    {
                                        <i class="@(PaginationParams.Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line")"></i>
                                    }
                                </th>
                                <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("valortotal")'>
                                    Valor Total
                                    @if (PaginationParams.SortColumn == "valortotal")
                                    {
                                        <i class="@(PaginationParams.Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line")"></i>
                                    }
                                </th>
                                <th scope="col">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Paginated == null)
                            {
                                <tr>
                                    <td colspan="5">Carregando...</td>
                                </tr>
                            }
                            else if (Paginated.Items.Count == 0)
                            {
                                <tr>
                                    <td colspan="5">Nenhuma compra encontrada!</td>
                                </tr>
                            }
                            else
                            {
                                @foreach (var item in Paginated.Items)
                                {
                                    <tr>
                                        <td>
                                            @item.DataCompra.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            @item.Fornecedor?.Nome
                                        </td>
                                        <td>
                                            @if (item.NFeEntrada != null)
                                            {
                                                @item.NFeEntrada.Numero
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @item.ValorTotal.ToString("C")
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="@Application.Routes.GerarRota(Application.Routes.CompraEditar, item.Id.ToString())" 
                                                   class="btn btn-sm btn-soft-info">
                                                    <i class="ri-edit-line"></i>
                                                </a>
                                                <button @onclick="() => Excluir(item)" type="button" 
                                                        class="btn btn-sm btn-soft-danger">
                                                    <i class="ri-delete-bin-line"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                @if (Paginated != null)
                {
                    <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
                }
            </div>
        </div>
    </div>
</div>

@code {
    private PaginationParameters PaginationParams = new PaginationParameters(1, 20);
    private PaginatedList<Compra>? Paginated;

    protected override async Task OnInitializedAsync()
    {
        PaginationParams.PageSize = Configuration.GetValue<int>("Pagination:PageSize");
        await LoadDataAsync();
    }

    private async Task Excluir(Compra entidade)
    {
        if (await AlertService.ShowConfirmDelete())
        {
            try
            {
                var result = await MessageBus.InvokeAsync<Result>(new DeleteCompra(entidade.Id));
                
                if (result.IsSuccess)
                {
                    await AlertService.ShowSuccessMessage("Compra excluída com sucesso!");
                    await LoadDataAsync();
                }
                else
                {
                    await AlertService.ShowError("Erro!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao excluir compra");
                }
            }
            catch (Exception ex)
            {
                await AlertService.ShowError("Erro!", $"Erro inesperado: {ex.Message}");
            }
        }
    }

    private async Task OnSearch()
    {
        PaginationParams.PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        PaginationParams.PageIndex = pageNumber;
        await LoadDataAsync();
    }

    private async Task OnSort(string columnName)
    {
        if (PaginationParams.SortColumn == columnName)
        {
            PaginationParams.Ascending = !PaginationParams.Ascending;
        }
        else
        {
            PaginationParams.SortColumn = columnName;
            PaginationParams.Ascending = true;
        }
        PaginationParams.PageIndex = 1;
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            var empresaId = await GetEmpresaIdAsync();
            var request = new ListCompras
            {
                EmpresaId = empresaId,
                PageIndex = PaginationParams.PageIndex,
                PageSize = PaginationParams.PageSize,
                SearchWord = PaginationParams.SearchWord,
                SortColumn = PaginationParams.SortColumn,
                Ascending = PaginationParams.Ascending,
                Filtro = new CompraFiltroRequest
                {
                    PalavraBusca = PaginationParams.SearchWord
                }
            };

            var result = await MessageBus.InvokeAsync<Result<PaginatedList<Compra>>>(request);
            
            if (result.IsSuccess)
            {
                Paginated = result.Value;
            }
            else
            {
                await AlertService.ShowError("Erro!", result.Errors.FirstOrDefault()?.Message ?? "Erro ao carregar compras");
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Erro!", $"Erro inesperado: {ex.Message}");
        }
    }
}
