@using Otikka.Application.Features.Users.Queries.GetUserEmpresas
@using EmpresaModel = Otikka.Domain.Entities.EmpresaModule.Empresa
@using Otikka.Application.Features.Users.Queries.GetUserLogin
@attribute [Route(Application.Routes.EmpresaSelecao)]
@layout BaseLayout
@inherits PageBase

<PageTitle>Selecionar Empresa</PageTitle>

<div class="auth-bg d-flex min-vh-100 justify-content-center align-items-center">
    <div class="row g-0 justify-content-center w-100 m-xxl-5 px-xxl-4 m-3">
        <div class="col-xl-6 col-lg-7 col-md-8">
            <div class="card overflow-hidden text-center h-100 p-xxl-4 p-3 mb-0">
                <a href="/" class="auth-brand mb-3">
                    <img src="images/logo-dark.png" alt="logo escuro" height="24" class="logo-dark">
                    <img src="images/logo.png" alt="logo claro" height="24" class="logo-light">
                </a>

                @if (Loading)
                {
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2">Carregando suas empresas...</p>
                    </div>
                }
                else if (Empresas == null || !Empresas.Any())
                {
                    <h3 class="fw-semibold mb-2">Bem-vindo ao Sistema!</h3>
                    <p class="text-muted mb-4">Você ainda não possui vínculo com nenhuma empresa. Escolha uma das opções abaixo:</p>

                    <Message SuccessMessage="@SuccessMessage" ErrorMessage="@ErrorMessage" WarningMessage="@WarningMessage" />

                    <div class="d-flex gap-3 justify-content-center">
                        <button type="button" class="btn btn-primary" @onclick="NavigateToRegisterCompany">
                            <i class="ri-building-line me-2"></i>Cadastrar Nova Empresa
                        </button>
                        <button type="button" class="btn btn-outline-primary" @onclick="RequestCompanyAccess">
                            <i class="ri-user-add-line me-2"></i>Solicitar Acesso a Empresa
                        </button>
                    </div>
                }
                else
                {
                    <h3 class="fw-semibold mb-2">Selecione uma Empresa</h3>
                    <p class="text-muted mb-4">Você possui acesso às seguintes empresas. Selecione uma para continuar:</p>

                    <Message SuccessMessage="@SuccessMessage" ErrorMessage="@ErrorMessage" WarningMessage="@WarningMessage" />

                    <div class="row">
                        @foreach (var empresa in Empresas)
                        {
                            <div class="col-md-6 mb-3">
                                <div class="card company-card h-100" @onclick="() => SelectCompany(empresa)" style="cursor: pointer;">
                                    <div class="card-body text-center">
                                        @if (!string.IsNullOrEmpty(empresa.Logo))
                                        {
                                            <img src="@empresa.Logo" alt="Logo da empresa" class="company-logo mb-3" style="max-height: 60px; max-width: 100%;">
                                        }
                                        else
                                        {
                                            <div class="company-placeholder mb-3">
                                                <i class="ri-building-line fs-24"></i>
                                            </div>
                                        }
                                        <h5 class="card-title mb-1">@empresa.NomeFantasia</h5>
                                        <p class="card-text text-muted small mb-3">@empresa.RazaoSocial</p>
                                        <button type="button" class="btn btn-primary btn-sm" disabled="@Processing">
                                            @if (Processing && SelectedCompanyId == empresa.Id)
                                            {
                                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                                <span>Selecionando...</span>
                                            }
                                            else
                                            {
                                                <span>Selecionar</span>
                                            }
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                    <div class="mt-3">
                        <button type="button" class="btn btn-primary" @onclick="NavigateToRegisterCompany">
                            <i class="ri-building-line me-2"></i>Cadastrar Nova Empresa
                        </button>
                    </div>
                }

                <p class="mb-0 mt-4">
                    @FullYear © @Application.Config.Name - Por <span class="fw-bold text-decoration-underline text-uppercase text-reset fs-12">Coderthemes</span>
                </p>
            </div>
        </div>
    </div>
</div>

@code {
    private List<EmpresaModel>? Empresas;
    private bool Loading = true;
    private Guid? SelectedCompanyId;
    public string FullYear { get; set; } = DateTime.Now.Year.ToString();

    protected override async Task OnInitializedAsync()
    {
        await CarregarEmpresas();
    }

    private async Task CarregarEmpresas()
    {
        try
        {
            Loading = true;

            var user = await LocalStorage.GetItemAsync<LoginResponse>("user");
            if (user == null)
            {
                NavigationManager.NavigateTo(Application.Routes.Login, forceLoad: true);
                return;
            }

            var query = new GetUserEmpresas { UserId = user.Id };
            var result = await MessageBus.InvokeAsync<Result<List<EmpresaModel>>>(query);

            if (result.IsSuccess)
            {
                Empresas = result.Value;
            }
            else
            {
                ErrorMessage = "Erro ao carregar empresas: " + string.Join("; ", result.Errors.Select(e => e.Message));
                Empresas = new List<EmpresaModel>();
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = "Erro inesperado: " + ex.Message;
            Empresas = new List<EmpresaModel>();
        }
        finally
        {
            Loading = false;
        }
    }

    private async Task SelectCompany(EmpresaModel empresa)
    {
        try
        {
            SelectedCompanyId = empresa.Id;
            await ProcessingChange(true);

            // Salva a empresa selecionada no localStorage
            await LocalStorage.SetItemAsync<EmpresaModel>(Application.Storage.Company, empresa);

            // Redireciona para o dashboard
            NavigationManager.NavigateTo(Application.Routes.Dashboard);
        }
        catch (Exception ex)
        {
            ErrorMessage = "Erro ao selecionar empresa: " + ex.Message;
        }
        finally
        {
            SelectedCompanyId = null;
            await ProcessingChange(false);
        }
    }
    private void NavigateToRegisterCompany()
    {
        NavigationManager.NavigateTo(Application.Routes.EmpresaCadastrar, forceLoad: true);
    }

    private void RequestCompanyAccess()
    {
        // TODO: Implementar solicitação de acesso a empresa
        WarningMessage = "Funcionalidade de solicitação de acesso em desenvolvimento.";
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync("updateDocumentTitle", "Selecionar Empresa");
            await JsRuntime.InvokeVoidAsync("applyThemeConfig");
            await JsRuntime.InvokeVoidAsync("loadApps");
            FullYear = DateTime.Now.Year.ToString();
            StateHasChanged();
        }
    }
}