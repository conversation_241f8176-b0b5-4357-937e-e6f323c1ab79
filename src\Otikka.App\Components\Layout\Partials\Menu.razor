<ul class="side-nav" id="two-col-menu">
    <li class="side-nav-title">Menu</li>

    <li class="side-nav-item">
        <NavLink href="@Application.Routes.OrdemServicoListar"
           class="side-nav-link">
            <span class="menu-icon"><i class="ri-honour-line"></i></span> <span class="menu-text">Ordem de serviço</span>
        </NavLink>
    </li>
    <li class="side-nav-item">
        <NavLink href="@Application.Routes.VendaListar"
           class="side-nav-link">
            <span class="menu-icon">
                <i class="ri-shopping-basket-line"></i>
            </span> <span class="menu-text">Vendas</span>
        </NavLink>
    </li>
    <li class="side-nav-item">
        <NavLink href="@Application.Routes.FornecedorListar" class="side-nav-link">
            <span class="menu-icon"><i class="mdi mdi-truck-delivery-outline"></i></span> <span class="menu-text">Fornecedor</span>
        </NavLink>
    </li>
    <li class="side-nav-item">
        <NavLink href="@Application.Routes.CompraListar" class="side-nav-link">
            <span class="menu-icon"><i class="ri-shopping-cart-line"></i></span> <span class="menu-text">Compras</span>
        </NavLink>
    </li>
    <li class="side-nav-item">
        <NavLink href="@Application.Routes.ClienteListar"
           class="side-nav-link">
            <span class="menu-icon">
                <i class="mdi mdi-account-supervisor"></i>
            </span> <span class="menu-text">Clientes</span>
        </NavLink>
    </li>
    <li class="side-nav-item">
        <NavLink href="@Application.Routes.ReceitaListar"
           class="side-nav-link">
            <span class="menu-icon">
                <i class="mdi mdi-account-supervisor"></i>
            </span> <span class="menu-text">Receita</span>
        </NavLink>
    </li>


    <li class="side-nav-item">
        <NavLink data-bs-toggle="collapse" href="#sideProduto" aria-expanded="false" aria-controls="sideProduto"
           class="side-nav-link">
            <span class="menu-icon"><i class="bx bx-package"></i></span>
            <span class="menu-text">Produtos</span>
            <span class="menu-arrow"></span>
        </NavLink>
        <div class="collapse" id="sideProduto">
            <ul class="sub-menu">
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.CategoriaProdutoListar"
                             class="side-nav-link"> <span class="menu-text">Categorias</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.MarcaListar"
                             class="side-nav-link"> <span class="menu-text">Marcas</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.ProdutoListar"
                             class="side-nav-link"> <span class="menu-text">Produtos</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.MovimentacaoEstoqueListar" class="side-nav-link"><span class="menu-text">Estoque</span> </NavLink>
                </li>
            </ul>
        </div>
    </li>

    <li class="side-nav-item">
        <NavLink data-bs-toggle="collapse" href="#sidebarAccount" aria-expanded="false" aria-controls="sidebarAccount"
           class="side-nav-link">
            <span class="menu-icon"><i class="ri-money-dollar-circle-line"></i></span>
            <span class="menu-text">Contas</span>
            <span class="menu-arrow"></span>
        </NavLink>
        <div class="collapse" id="sidebarAccount">
            <ul class="sub-menu">
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.GerarRota(Application.Routes.TransacaoFinanceiraListar, "Pagamento")"
                       class="side-nav-link"> <span class="menu-text">Pagar</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.GerarRota(Application.Routes.TransacaoFinanceiraListar, "Recebimento")"
                       class="side-nav-link"> <span class="menu-text">Receber</span> </NavLink>
                </li>
            </ul>
        </div>
    </li>

    <li class="side-nav-item">
        <NavLink data-bs-toggle="collapse" href="#sidebarRelatorio" aria-expanded="false"
           aria-controls="sidebarRelatorio" class="side-nav-link">
            <span class="menu-icon"><i class="ri-bar-chart-2-fill"></i></span>
            <span class="menu-text">Relatórios</span>
            <span class="menu-arrow"></span>
        </NavLink>
        <div class="collapse" id="sidebarRelatorio">
            <ul class="sub-menu">
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.RelatorioGeral"
                       class="side-nav-link"> <span class="menu-text">Geral</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.RelatorioReceitasVencidas"
                       class="side-nav-link"> <span class="menu-text">Receitas vencidas</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.RelatorioOSVendas"
                       class="side-nav-link"> <span class="menu-text">OS e Vendas</span> </NavLink>
                </li>
            </ul>
        </div>
    </li>

    <li class="side-nav-title mt-2">Configurações</li>
    <li class="side-nav-item">
        <NavLink data-bs-toggle="collapse" href="#sidebarRegisters" aria-expanded="false"
           aria-controls="sidebarRegisters" class="side-nav-link">
            <span class="menu-icon">
                <i class="ri-settings-4-line"></i>
            </span> <span class="menu-text">Configurações</span> <span class="menu-arrow"></span>
        </NavLink>
        <div class="collapse" id="sidebarRegisters">
            <ul class="sub-menu">
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.EmpresaListar"
                       class="side-nav-link"> <span class="menu-text">Empresas</span> </NavLink>
                </li>

                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.ColaboradorListar"
                       class="side-nav-link"> <span class="menu-text">Colaboradores</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.FormaPagamentoListar"
                       class="side-nav-link"> <span class="menu-text">Formas de pagamento</span> </NavLink>
                </li>
                <li class="side-nav-item">
                    <NavLink href="@Application.Routes.FinanceiroCategoriaListar"
                       class="side-nav-link"> <span class="menu-text">Categorias de Contas</span> </NavLink>
                </li>
            </ul>
        </div>
    </li>

    <li class="side-nav-item">
        <NavLink href="@Application.Routes.Suporte" class="side-nav-link">
            <span class="menu-icon"><i class="ri-phone-line"></i></span>
            <span class="menu-text">Suporte</span>
        </NavLink>
    </li>
</ul>