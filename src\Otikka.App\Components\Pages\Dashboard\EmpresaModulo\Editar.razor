@using Otikka.App.Components.Pages.Dashboard.EmpresaModulo.Componentes
@using Otikka.Application.Features.Empresa.Commands.UpdateEmpresa
@using Otikka.Application.Features.Empresa.Queries.GetCompany
@using Otikka.Domain.Entities.UsuarioModule
@attribute [Route(Application.Routes.EmpresaEditar)]
@inherits PageBase
@inject ILogger<Editar> Logger
    
<PageTitle>
    Empresa - Edição
</PageTitle>
<PageBreadcrumb Title="Empresa" SubTitle="Edição" />

@if (Empresa == null)
{
    <div class="text-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Carregando...</span>
        </div>
        <p>Carregando dados da empresa...</p>
    </div>
}
else
{
    <EmpresaForm EhEdicao="@true" Empresa="Empresa" OnSave="Submit" />
}

@code {
    [Parameter] public Guid Id { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync("applyThemeConfig");
            await JsRuntime.InvokeVoidAsync("loadApps");
        }
    }

    [SupplyParameterFromForm] 
    private UpdateEmpresa? Empresa { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var query = new GetEmpresa { Id = Id };
            var result = await MessageBus.InvokeAsync<Result<Empresa>>(query);
            
            if (result.IsFailed || result.Value == null)
            {
                ErrorMessage = result.Errors.FirstOrDefault()?.Message ?? "Empresa não encontrada.";
                return;
            }

            var empresa = result.Value;
            Empresa = new UpdateEmpresa
            {
                Id = empresa.Id,
                Version = empresa.Version,
                RazaoSocial = empresa.RazaoSocial,
                NomeFantasia = empresa.NomeFantasia,
                Documento = empresa.Documento,
                Sigla = empresa.Sigla,
                Email = empresa.Email,
                Telefone = empresa.Telefone,
                TipoNumeracaoOrdemServico = empresa.TipoNumeracaoOrdemServico,
                TipoNumeracaoVenda = empresa.TipoNumeracaoVenda,
                TipoUnidade = empresa.TipoUnidade,
                EmpresaPaiId = empresa.EmpresaPaiId,
                UsuarioId = empresa.UsuarioId,
                LogoLargura = empresa.LogoLargura,
                LogoAltura = empresa.LogoAltura,
                Endereco = empresa.Endereco ?? new()
            };
            
            // Define o Logo da entidade base (string) para exibição
            ((Empresa)Empresa).Logo = empresa.Logo;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao carregar empresa com ID: {Id}", Id);
            ErrorMessage = "Erro ao carregar dados da empresa.";
        }
    }

    private async Task Submit()
    {
        try
        {
            if (Empresa == null)
            {
                ErrorMessage = "Dados da empresa não carregados.";
                return;
            }

            // Log para debugging
            Logger.LogInformation("Iniciando atualização de empresa {Id} pelo usuário {UserId}", Empresa.Id, await GetUsuarioIdLoggedAsync());

            // Define o ID do usuário atual para auditoria
            Empresa.UsuarioId = await GetUsuarioIdLoggedAsync();

            // Invoca o comando UpdateEmpresa via MessageBus
            var result = await MessageBus.InvokeAsync<Result>(Empresa);

            if (result.IsSuccess)
            {
                Logger.LogInformation("Empresa {Id} atualizada com sucesso", Empresa.Id);
                SuccessMessage = "Empresa atualizada com sucesso!";
                
                // Redireciona para a lista após 2 segundos
                NavigationManager.NavigateTo(Application.Routes.EmpresaListar);
            }
            else
            {
                var errorMessages = result.Errors.Select(e => e.Message).ToList();
                ErrorMessage = string.Join("; ", errorMessages);
                Logger.LogWarning("Falha ao atualizar empresa {Id}: {Errors}", Empresa.Id, ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado ao atualizar empresa {Id}: {Message}", Empresa?.Id, ex.Message);
            ErrorMessage = "Erro inesperado ao atualizar empresa. Tente novamente.";
        }
    }
}