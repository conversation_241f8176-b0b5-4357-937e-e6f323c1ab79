using Microsoft.EntityFrameworkCore;
using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using Otikka.Application.Models.Requests;
using Otikka.Application.Models.Responses;
using Otikka.Domain.Common.ExtensionMethods;
using Otikka.Domain.Entities.Common;
using Otikka.Domain.Entities.OrdemServicoModulo;
using Otikka.Domain.Entities.TransacaoFinanceiraModulo;
using Otikka.Domain.Entities.VendaModulo;
using Otikka.Domain.Enums;

namespace Otikka.Persistence.Repositories;

public class VendaRepositorio : IVendaRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;

    public VendaRepositorio(IDbContextFactory<ApplicationDbContext> factory)
    {
        _factory = factory;
    }

    public async Task<string> GerarNumeroIdentificador(Empresa empresa)
    {
        string numeroIdentificador = string.Empty;
        if (empresa.TipoNumeracaoVenda != Domain.Enums.TipoAlgoritmoGeradorId.Manual)
        {
            if (empresa.TipoNumeracaoVenda ==
                TipoAlgoritmoGeradorId.Automatico)
            {
                string? identificadorMax = await ObterUltimoNumeroVendaPorEmpresa(empresa.Id);

                if (identificadorMax is null) return "1";

                int id;
                if (int.TryParse(identificadorMax, out id))
                    numeroIdentificador = (id + 1).ToString();
            }
        }

        return numeroIdentificador;
    }

    public async Task<string?> ObterUltimoNumeroVendaPorEmpresa(Guid empresaId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        return await _db.Vendas.Where(a => a.EmpresaId == empresaId).MaxAsync(a => a.NumeroIdentificador);
    }



    public async Task<PaginatedList<Venda>> ObterTudo(Guid empresaId, VendaFiltroRequest? filtro, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            var query = _db.Vendas.Where(a=>a.EmpresaId == empresaId).AsQueryable();

            if (filtro is not null)
            {

                if (!string.IsNullOrWhiteSpace(filtro.PalavraBusca))
                    query = query.Where(a =>
                        (a.Cliente != null && a.Cliente.Nome != null && a.Cliente.Nome.Contains(filtro.PalavraBusca)) ||
                        (a.NumeroIdentificador != null && a.NumeroIdentificador.Contains(filtro.PalavraBusca))
                    );

                if (filtro.DataVendaInicio.HasValue)
                    query = query.Where(a => a.DataVenda >= filtro.DataVendaInicio.Value.InicioDia());

                if (filtro.DataVendaTermino.HasValue)
                    query = query.Where(a => a.DataVenda <= filtro.DataVendaTermino.Value.FinalDia());

                if (filtro.VendedorId.HasValue)
                    query = query.Where(a => a.VendedorId == filtro.VendedorId.Value);
            }

            // Aplicar ordenação
            query = ApplySortVenda(query, paginationParams.SortColumn, paginationParams.Ascending);

            var items = await query
                .Include(a => a.Cliente)
                .Include(a => a.Vendedor)
                .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
                .Take(paginationParams.PageSize)
                .ToListAsync();

            var count = await query.CountAsync();

            int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

            return new PaginatedList<Venda>(items, paginationParams.PageIndex, totalPages, paginationParams.SortColumn, paginationParams.Ascending);
        }
    }

    public async Task<Venda?> Obter(Guid empresaId, Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        {
            return await _db.Vendas
                .Where(a=>a.EmpresaId == empresaId && a.Id == id)
                .Include(a => a.TransacaoFinanceira!.Pagamentos!.Where(p => p.DataExclusao == null)).ThenInclude(a => a.FormaPagamento)
                .Include(a => a.ProdutoServicoVendidos!).ThenInclude(a => a.Produto)
                .SingleOrDefaultAsync();
        }
    }

    public async Task Cadastrar(Venda entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            // Limpar propriedades de navegação para evitar problemas com o EF Core
            if (entity.ProdutoServicoVendidos is not null && entity.ProdutoServicoVendidos.Count > 0)
            {
                foreach (var p in entity.ProdutoServicoVendidos)
                {
                    p.Produto = null;
                }
            }

            _db.Vendas.Add(entity);
            await _db.SaveChangesAsync();

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw; // Re-throw the exception to be handled by the calling code
        }
    }

    public async Task Atualizar(Venda entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();
        try
        {
            var entidade = (Venda)entity.Clone();

            // Primeira etapa: Atualizar campos básicos (sem controle de estoque manual)
            await AtualizarCamposBasicos(_db, entity);

            // Segunda etapa: Atualizar produtos vendidos
            if (entity.ProdutoServicoVendidos != null)
            {
                await AtualizarProdutosVendidosSeparadamente(_db, entity);
            }

            // Terceira etapa: Atualizar transação financeira e pagamentos
            if (entity.TransacaoFinanceira != null)
            {
                await AtualizarTransacaoFinanceiraSeparadamente(_db, entity);
            }

            await _db.SaveChangesAsync();

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    private static async Task AtualizarCamposBasicos(ApplicationDbContext db, Venda entity)
    {
        // Limpa o ChangeTracker
        db.ChangeTracker.Clear();

        // Carrega apenas a entidade principal COM tracking
        var originalEntity = await db.Vendas
            .SingleOrDefaultAsync(o => o.Id == entity.Id);

        if (originalEntity == null)
        {
            throw new InvalidOperationException("Venda não encontrada.");
        }

        // Atualiza campos básicos da venda
        originalEntity.DataAtualizacao = entity.DataAtualizacao;
        originalEntity.AtualizadoPorId = entity.AtualizadoPorId;
        originalEntity.ClienteId = entity.ClienteId;
        originalEntity.VendedorId = entity.VendedorId;
        originalEntity.NumeroIdentificador = entity.NumeroIdentificador;
        originalEntity.Observacao = entity.Observacao;
        originalEntity.DataVenda = entity.DataVenda;

        // Salva mudanças básicas
        await db.SaveChangesAsync();
    }

    private static async Task AtualizarProdutosVendidosSeparadamente(ApplicationDbContext db, Venda entity)
    {
        var produtosAntigos = await db.ProdutoServicoVendidos
            .Where(p => p.TransacaoComercialId == entity.Id)
            .ToListAsync();

        var produtosAtuais = entity.ProdutoServicoVendidos ?? new List<ProdutoServicoVendido>();

        var produtosParaRemover = produtosAntigos
            .Where(antigo => !produtosAtuais.Any(novo => novo.Id == antigo.Id))
            .ToList();

        if (produtosParaRemover.Count > 0)
        {
            db.ProdutoServicoVendidos.RemoveRange(produtosParaRemover);
        }

        foreach (var psv in produtosAtuais)
        {
            var existingPsv = produtosAntigos.FirstOrDefault(p => p.Id == psv.Id);
            if (existingPsv != null)
            {
                db.Entry(existingPsv).CurrentValues.SetValues(psv);
            }
            else
            {
                psv.TransacaoComercialId = entity.Id;
                psv.Id = psv.Id == Guid.Empty ? Guid.NewGuid() : psv.Id;
                db.ProdutoServicoVendidos.Add(psv);
            }
        }
    }
    private static async Task AtualizarTransacaoFinanceiraSeparadamente(ApplicationDbContext db, Venda entity)
    {
        if (entity.TransacaoFinanceira == null)
        {
            return;
        }

        var transacaoExistente = await db.TransacoesFinanceiras
            .Include(t => t.Pagamentos)
            .FirstOrDefaultAsync(t => t.TransacaoComercialId == entity.Id);

        if (transacaoExistente != null)
        {
            db.Entry(transacaoExistente).CurrentValues.SetValues(entity.TransacaoFinanceira);

            if (entity.TransacaoFinanceira.Pagamentos != null)
            {
                var pagamentosAntigos = transacaoExistente.Pagamentos?.ToList() ?? new List<Pagamento>();
                var pagamentosAtuais = entity.TransacaoFinanceira.Pagamentos ?? new List<Pagamento>();

                var pagamentosParaRemover = pagamentosAntigos
                    .Where(antigo => !pagamentosAtuais.Any(novo => novo.Id == antigo.Id))
                    .ToList();

                if (pagamentosParaRemover.Any())
                {
                    db.Pagamentos.RemoveRange(pagamentosParaRemover);
                }

                foreach (var pag in pagamentosAtuais)
                {
                    var existingPag = pagamentosAntigos.FirstOrDefault(p => p.Id == pag.Id);
                    if (existingPag != null)
                    {
                        db.Entry(existingPag).CurrentValues.SetValues(pag);
                    }
                    else
                    {
                        pag.TransacaoFinanceiraId = transacaoExistente.Id;
                        pag.Id = pag.Id == Guid.Empty ? Guid.NewGuid() : pag.Id;
                        db.Pagamentos.Add(pag);
                    }
                }
            }
        }
        else
        {
            entity.TransacaoFinanceira.TransacaoComercialId = entity.Id;
            entity.TransacaoFinanceira.TipoTransacaoComercial = TipoTransacaoComercial.Venda;
            db.TransacoesFinanceiras.Add(entity.TransacaoFinanceira);
        }
    }
    public async Task Excluir(Guid empresaId, Guid id)
    {
        var entity = await Obter(empresaId, id);
        if (entity is not null)
            await Excluir(entity);
    }

    public async Task Excluir(Venda entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        using var transaction = await _db.Database.BeginTransactionAsync();

        try
        {
            _db.Vendas.Remove(entity);
            await _db.SaveChangesAsync();

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    // Métodos para relatórios
    public async Task<int> ObterQuantidadeTotal(Guid empresaId, RelatorioFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        var query = _db.Vendas.Where(a => a.EmpresaId == empresaId).AsQueryable();
        
        query = AplicarFiltrosRelatorio(query, filtro);
        
        return await query.CountAsync();
    }

    public async Task<decimal> ObterValorTotal(Guid empresaId, RelatorioFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        var query = _db.Vendas
            .Where(a => a.EmpresaId == empresaId)
            .AsQueryable();
        
        query = AplicarFiltrosRelatorio(query, filtro);
        
        return await query
            .Include(a => a.TransacaoFinanceira)
            .Where(a => a.TransacaoFinanceira != null)
            .SumAsync(a => a.TransacaoFinanceira!.ValorTotal ?? 0);
    }

    private static IQueryable<Venda> AplicarFiltrosRelatorio(IQueryable<Venda> query, RelatorioFiltroRequest filtro)
    {
        if (filtro.VendedorId.HasValue && filtro.VendedorId != Guid.Empty)
            query = query.Where(a => a.VendedorId == filtro.VendedorId);

        if (filtro.DataInicio.HasValue)
            query = query.Where(a => a.DataVenda >= filtro.DataInicio.Value.InicioDia());

        if (filtro.DataTermino.HasValue)
            query = query.Where(a => a.DataVenda <= filtro.DataTermino.Value.FinalDia());

        return query;
    }

    private IQueryable<Venda> ApplySortVenda(IQueryable<Venda> query, string sortColumn, bool ascending)
    {
        if (string.IsNullOrWhiteSpace(sortColumn))
        {
            return query.OrderByDescending(a => a.DataCriacao);
        }

        return sortColumn.ToLower() switch
        {
            "numeroidentificador" => ascending ? query.OrderBy(a => a.NumeroIdentificador) : query.OrderByDescending(a => a.NumeroIdentificador),
            "datavenda" => ascending ? query.OrderBy(a => a.DataVenda) : query.OrderByDescending(a => a.DataVenda),
            "datacriacao" => ascending ? query.OrderBy(a => a.DataCriacao) : query.OrderByDescending(a => a.DataCriacao),
            "valortotal" => ascending ? query.OrderBy(a => a.TransacaoFinanceira!.ValorTotal) : query.OrderByDescending(a => a.TransacaoFinanceira!.ValorTotal),
            "cliente" => ascending ? query.OrderBy(a => a.Cliente!.Nome) : query.OrderByDescending(a => a.Cliente!.Nome),
            "vendedor" => ascending ? query.OrderBy(a => a.Vendedor!.Nome) : query.OrderByDescending(a => a.Vendedor!.Nome),
            _ => query.OrderByDescending(a => a.DataCriacao)
        };
    }

    public async Task<List<VendaRelatorioItem>> ObterParaRelatorioOSVendas(Guid empresaId, RelatorioOSVendasFiltroRequest filtro)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        var query = _db.Vendas
            .Where(a => a.EmpresaId == empresaId)
            .Include(a => a.Cliente)
            .Include(a => a.Vendedor)
            .Include(a => a.TransacaoFinanceira)
            .AsQueryable();

        // Aplicar filtros específicos do relatório
        // Para vendas, usamos a DataVenda como data de emissão
        if (filtro.DataEmissaoInicio.HasValue)
            query = query.Where(a => a.DataVenda >= filtro.DataEmissaoInicio.Value.InicioDia());

        if (filtro.DataEmissaoFim.HasValue)
            query = query.Where(a => a.DataVenda <= filtro.DataEmissaoFim.Value.FinalDia());

        // Vendas não têm previsão de entrega, então ignoramos esses filtros
        // Mas se quisermos incluir vendas quando há filtro de previsão, podemos fazer isso:
        // (não aplicamos filtro de previsão para vendas)

        if (filtro.VendedorId.HasValue && filtro.VendedorId != Guid.Empty)
            query = query.Where(a => a.VendedorId == filtro.VendedorId);

        return await query
            .Select(a => new VendaRelatorioItem
            {
                Id = a.Id,
                NumeroIdentificador = a.NumeroIdentificador,
                DataVenda = a.DataVenda,
                ClienteNome = a.Cliente != null ? a.Cliente.Nome : "Cliente não informado",
                VendedorNome = a.Vendedor != null ? a.Vendedor.Nome : "Vendedor não informado",
                ValorTotal = a.TransacaoFinanceira != null ? a.TransacaoFinanceira.ValorTotal ?? 0 : 0,
                ValorPago = a.TransacaoFinanceira != null ? a.TransacaoFinanceira.ValorPago ?? 0 : 0,
                ValorAPagar = a.TransacaoFinanceira != null ? a.TransacaoFinanceira.ValorRestante ?? 0 : 0
            })
            .OrderByDescending(a => a.DataVenda)
            .ToListAsync();
    }
}