@using Otikka.Application.Features.Empresa.Commands.DeleteEmpresa
@using Otikka.Application.Features.Empresa.Queries.ListCompanies
@using Otikka.Application.Models
@using Otikka.Domain.Entities.UsuarioModule
@using Otikka.Application.Constants
@attribute [Route(Application.Routes.EmpresaListar)]
@inherits PageBase

<PageTitle>
    Empresa - Lista
</PageTitle>

<div class="page-title-head d-flex align-items-sm-center flex-sm-row flex-column gap-2">
    <div class="flex-grow-1">
        <h4 class="fs-18 fw-semibold mb-0">Empresas</h4>
    </div>

    <div class="text-end">
        <ol class="breadcrumb m-0 py-0">
            <li class="breadcrumb-item"><a href="javascript: void(0);">Osen</a></li>
            <li class="breadcrumb-item"><a href="javascript: void(0);">Tables</a></li>
            <li class="breadcrumb-item active">Basic Tables</li>
        </ol>
    </div>
</div>
<div class="card">
    <div class="card-header border-bottom border-dashed d-flex align-items-center justify-content-between">
            <h4 class="header-title">Filtros</h4>
            <div class="flex-shrink-0">
                <button type="button" @onclick="async () => await NavigateToCreateAsync()" class="btn btn-primary btn-sm material-shadow-none">
                    <i class="ri-file-List-3-line align-middle"></i> Cadastrar
                </button>
            </div>
    </div><!-- end card header -->

    <div class="card-body">
        <div class="col-lg-6">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Digite sua pesquisa" @bind="Request.SearchWord">
                <button class="btn btn-outline-secondary" type="button" @onclick="OnSearch">
                    <i class="ti ti-search"></i>
                </button>
            </div>
        </div>
        <Message SuccessMessage="@SuccessMessage" ErrorMessage="@ErrorMessage" WarningMessage="@WarningMessage" />
        <div class="table-responsive mt-4 table-margin-width-24">
            <table class="table table-borderless table-centered align-middle table-nowrap mb-0">
                <thead class="text-muted table-light">
                    <tr>
                        <th scope="col" style="width: 150px; cursor: pointer;" @onclick='() => OnSort("RazaoSocial")'>
                            Nome fantasia
                            @if (Request.SortColumn == "RazaoSocial")
                            {
                                <i class="@(Request.Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line") ms-1"></i>
                            }
                        </th>
                        <th scope="col" style="cursor: pointer;" @onclick='() => OnSort("Documento")'>
                            CNPJ
                            @if (Request.SortColumn == "Documento")
                            {
                                <i class="@(Request.Ascending ? "ri-arrow-up-line" : "ri-arrow-down-line") ms-1"></i>
                            }
                        </th>
                        <th scope="col">Endereço</th>
                        <th scope="col">Ação</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Paginated == null)
                    {
                        <tr>
                            <td colspan="5">Carregando...</td>
                        </tr>
                    }
                    else if (Paginated.Items.Count == 0)
                    {
                        <tr>
                            <td colspan="5">Nenhum registro!</td>
                        </tr>
                    }
                    else
                    {
                        @foreach (var item in Paginated.Items)
                        {
                            <tr>
                                <td style="width: 250px; max-width: 250px; white-space: normal; word-wrap: break-word; overflow-wrap: break-word;">
                                    @item.RazaoSocial
                                </td>
                                <td>
                                    @item.Documento
                                </td>
                                <td>
                                    @if (item.Endereco is not null)
                                    {
                                        <span>@item.Endereco.Estado - @item.Endereco.Cidade - @item.Endereco.Bairro <br />@item.Endereco.Logradouro</span>
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-soft-success" @onclick="() => SelecionarEmpresaAsync(item)"><i class=" ri-arrow-right-line me-1"></i>Selecionar</button>
                                    <div class="btn-group ms-2">
                                        <a href="@Application.Routes.GerarRota(Application.Routes.EmpresaEditar, item.Id.ToString())" class="btn btn-sm btn-soft-info">
                                            <i class="ri-edit-line"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-soft-danger" @onclick="() => ExcluirEmpresaAsync(item)" title="Excluir">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
        <Pagination Paginated="@Paginated" OnPageChanged="@OnPageChanged" />
    </div>
</div>

@code {
    ListCompanies Request = new ListCompanies();
    private PaginatedList<Empresa>? Paginated;

    protected override async Task OnInitializedAsync()
    {
        Request.PageSize = Configuration.GetValue<int>("Pagination:PageSize");
        Request.UserId = await GetUsuarioIdLoggedAsync();

        await CarregarDadosAsync();
    }

    private async Task OnSearch()
    {
        await CarregarDadosAsync();
    }

    private async Task OnPageChanged(int pageNumber)
    {
        Request.PageIndex = pageNumber;
        await CarregarDadosAsync();
    }

    private async Task CarregarDadosAsync()
    {
        var res = await MessageBus.InvokeAsync<Result<PaginatedList<Empresa>>>(Request);

        if (res.IsFailed)
        {
            ErrorMessage = res.Errors.FirstOrDefault()?.Message ?? "Erro ao carregar dados!";
            return;
        }

        Paginated = res.Value;
    }

    private async Task SelecionarEmpresaAsync(Empresa Company)
    {
        await LocalStorage.SetItemAsync<Empresa>(Application.Storage.Company, Company);

        // Envia notificação para outros componentes sobre a empresa selecionada
        MessagingCenter.Send(SystemMessages.EmpresaSelecionada, SystemMessages.EmpresaSelecionada, Company);
    }

    private async Task NavigateToCreateAsync()
    {
        // Limpa a empresa selecionada antes de navegar para criar nova
        await LocalStorage.RemoveItemAsync(Application.Storage.Company);
        
        // Navega para a página de cadastro
        NavigationManager.NavigateTo(Application.Routes.EmpresaCadastrar);
    }

    private async Task ExcluirEmpresaAsync(Empresa empresa)
    {
        try
        {
            // Pede confirmação do usuário
            var confirmacao = await JsRuntime.InvokeAsync<bool>("confirm", 
                $"Tem certeza que deseja excluir a empresa '{empresa.NomeFantasia}'?\n\nEsta ação não pode ser desfeita.");
            
            if (!confirmacao)
                return;

            // Executa a exclusão
            var command = new DeleteEmpresa { Id = empresa.Id };
            var result = await MessageBus.InvokeAsync<Result>(command);

            if (result.IsSuccess)
            {
                SuccessMessage = $"Empresa '{empresa.NomeFantasia}' excluída com sucesso!";
                
                // Recarrega a lista
                await CarregarDadosAsync();
            }
            else
            {
                var errorMessages = result.Errors.Select(e => e.Message).ToList();
                ErrorMessage = string.Join("; ", errorMessages);
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Erro inesperado ao excluir empresa: {ex.Message}";
        }
    }

    private async Task OnSort(string columnName)
    {
        if (Request.SortColumn == columnName)
        {
            // Se já está ordenando por esta coluna, inverte a direção
            Request.Ascending = !Request.Ascending;
        }
        else
        {
            // Se é uma nova coluna, define como crescente
            Request.SortColumn = columnName;
            Request.Ascending = true;
        }

        // Volta para a primeira página ao ordenar
        Request.PageIndex = 1;
        await CarregarDadosAsync();
    }
}