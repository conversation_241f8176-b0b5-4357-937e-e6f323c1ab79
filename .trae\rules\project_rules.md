# Descrição do Projeto

Otikka é um sistema ERP para uma rede de óticas, gerenciando clientes, ordens de serviço, vendas, produtos, estoque, estoque, lojas, funcionários, vendas, receitas oftalmológicas de clientes, fornecedores, contas a pagar e receber, relatórios, pagamentos de ordens de serviço, pagamentos de vendas e pagamentos de contas a receber e a pagar.
A maior parte do código foi escrita em C# e .NET, seguindo as regras de desenvolvimento .NET e os princípios de código limpo (arquitetura limpa). Utiliza o EntityFrameworkCore Code First com banco de dados (PostgreSQL).

# Pilha de Tecnologia

- .NET 9 + .NET Entity Framework Core 9 + .NET Blazor Server
- Banco de dados: PostgreSQL
- Validações: Biblioteca FluentValidation
- FluentResults para padrão de resultado
- Wolverine .NET para CQRS
- Hangfire para tarefas em segundo plano
- AWS S3 para armazenamento de arquivos
- AKSoftware.Blazor.Utilities para gerenciamento de estado entre componentes

# Padrões de Design

- Código Limpo / Arquitetura Limpo
- CQRS com Wolverine .NET
- Padrão de Resultado
- MessagingCenter para gerenciamento de estado entre componentes Blazor

# Política de Armazenamento de Arquivos

**IMPORTANTE: Todos os arquivos, fotos, documentos, imagens e qualquer tipo de conteúdo estático devem ser armazenados EXCLUSIVAMENTE na AWS S3.**

## Serviço de Armazenamento AWS S3

O projeto utiliza o [S3StorageService.cs](mdc:src/Otikka.Infrastructure/Storage/S3StorageService.cs) como implementação principal do [IFileStorageService](mdc:src/Otikka.Application/Contracts/Infrastructure/IFileStorageService.cs).

### Configuração da AWS S3

- **Região**: Configurada via `AWS:Region` no appsettings.json
- **Credenciais**: `AWS:AccessKey` e `AWS:SecretKey`
- **Criptografia**: Todos os arquivos são criptografados com AES256
- **Acesso Público**: Configurado como `S3CannedACL.PublicRead` para arquivos que precisam ser acessados publicamente

### Estrutura de Buckets Recomendada

```
otikka-{ambiente}/
├── usuarios/
│   ├── fotos/           # Fotos de perfil dos usuários
│   └── documentos/      # Documentos dos usuários
├── empresas/
│   ├── logotipos/       # Logotipos das empresas
│   └── documentos/      # Documentos das empresas
├── produtos/
│   ├── imagens/         # Imagens dos produtos
│   └── catalogos/       # Catálogos de produtos
├── ordens-servico/
│   ├── anexos/          # Anexos das ordens de serviço
│   └── receitas/        # Receitas oftalmológicas
├── vendas/
│   └── comprovantes/    # Comprovantes de vendas
└── documentos/
    ├── contratos/       # Contratos diversos
    └── relatorios/      # Relatórios gerados
```

### Uso do Serviço

```csharp
// Exemplo de uso do IFileStorageService
public class ExemploHandler
{
    private readonly IFileStorageService _fileStorageService;

    public ExemploHandler(IFileStorageService fileStorageService)
    {
        _fileStorageService = fileStorageService;
    }

    public async Task<Result<string>> UploadArquivo(Stream stream, string nomeArquivo, string contentType)
    {
        var bucketName = "otikka-producao"; // ou otikka-desenvolvimento
        var objectName = $"usuarios/fotos/{Guid.NewGuid()}-{nomeArquivo}";

        return await _fileStorageService.UploadFileAsync(bucketName, objectName, stream, contentType);
    }
}
```

### Tipos de Arquivos Suportados

- **Imagens**: JPG, PNG, GIF, WEBP
- **Documentos**: PDF, DOC, DOCX, XLS, XLSX
- **Arquivos de Texto**: TXT, CSV
- **Outros**: Conforme necessidade do negócio

### Regras de Nomenclatura de Arquivos

- Use sempre GUID para evitar conflitos: `{Guid.NewGuid()}-{nomeOriginal}`
- Organize por módulo: `usuarios/fotos/`, `produtos/imagens/`, etc.
- Use nomes descritivos: `receita-oftalmologica-{clienteId}-{data}.pdf`

### Configuração no appsettings.json

```json
{
  "AWS": {
    "Region": "us-east-1",
    "AccessKey": "sua-access-key",
    "SecretKey": "sua-secret-key"
  }
}
```

## Implementações Disponíveis

- **S3StorageService**: Implementação para AWS S3 (PADRÃO)
- **MinioStorageService**: Implementação para MinIO (apenas para desenvolvimento local)

### Dependências AWS S3

O projeto [Otikka.Infrastructure.csproj](mdc:src/Otikka.Infrastructure/Otikka.Infrastructure.csproj) inclui:

- `AWSSDK.S3` Version="4.0.4" - SDK oficial da AWS para S3
- `Minio` Version="6.0.5" - Cliente MinIO para desenvolvimento local

# Gerenciamento de Estado entre Componentes Blazor

**IMPORTANTE: Todo gerenciamento de estado entre componentes/telas do Blazor deve ser realizado usando a biblioteca AKSoftware.Blazor.Utilities.**

## Biblioteca AKSoftware.Blazor.Utilities

A biblioteca [AKSoftware.Blazor.Utilities](https://github.com/aksoftware98/blazor-utilities) fornece o `MessagingCenter` baseado no Xamarin.Forms MessagingCenter, permitindo comunicação entre componentes usando o padrão Publish-Subscribe.

### Características Principais

- **Comunicação Independente**: Permite enviar dados entre componentes independentemente da relação hierárquica (Parent-Child, etc.)
- **Padrão Publish-Subscribe**: Um componente envia uma mensagem e outros componentes podem se inscrever para recebê-la
- **Desacoplamento**: Componentes não precisam conhecer uns aos outros diretamente
- **Compatibilidade**: Funciona tanto em Blazor Server quanto em Blazor WebAssembly

### Instalação

```xml
<PackageReference Include="AKSoftware.Blazor.Utilities" Version="1.0.0" />
```

### Configuração

No `Program.cs` ou `Startup.cs`:

```csharp
using AKSoftware.Blazor.Utilities;

// Registra o MessagingCenter
builder.Services.AddMessagingCenter();
```

### Uso Básico

#### 1. Enviando Mensagens

```csharp
// Enviar mensagem simples
MessagingCenter.Send<ComponenteOrigem>(this, "nome_da_mensagem");

// Enviar mensagem com dados
MessagingCenter.Send<ComponenteOrigem, TipoDado>(this, "nome_da_mensagem", dadosParaEnviar);
```

#### 2. Recebendo Mensagens

```csharp
@implements IDisposable

@code {
    protected override void OnInitialized()
    {
        // Inscrição para mensagem simples
        MessagingCenter.Subscribe<ComponenteOrigem>(this, "nome_da_mensagem", (sender) =>
        {
            // Para Blazor Server, use InvokeAsync para atualizações de UI
            InvokeAsync(() =>
            {
                // Lógica para processar a mensagem
                StateHasChanged();
            });
        });

        // Inscrição para mensagem com dados
        MessagingCenter.Subscribe<ComponenteOrigem, TipoDado>(this, "nome_da_mensagem", (sender, dados) =>
        {
            InvokeAsync(() =>
            {
                // Processar os dados recebidos
                StateHasChanged();
            });
        });
    }

    public void Dispose()
    {
        // Sempre desinscrever ao descartar o componente
        MessagingCenter.Unsubscribe<ComponenteOrigem>(this, "nome_da_mensagem");
    }
}
```

### Exemplo Prático

#### Componente que Envia (Listar.razor)

```csharp
@page "/empresas"
@using Otikka.Domain.Entities.EmpresaModule

@code {
    private void SelecionarEmpresa(Empresa empresa)
    {
        // Envia mensagem para notificar seleção de empresa
        MessagingCenter.Send<Listar, Empresa>(this, "empresa_selecionada", empresa);
    }
}
```

#### Componente que Recebe (TopBar.razor)

```csharp
@using Otikka.Domain.Entities.EmpresaModule
@implements IDisposable

@code {
    private Empresa? empresaSelecionada;

    protected override void OnInitialized()
    {
        // Se inscreve para receber notificações de empresa selecionada
        MessagingCenter.Subscribe<Listar, Empresa>(SystemMessages.EmpresaSelecionada, SystemMessages.EmpresaSelecionada, async (sender, empresa) =>
        {
            // Para Blazor Server, use InvokeAsync para atualizações de UI
            await InvokeAsync(() =>
            {
                empresaSelecionada = empresa;
                StateHasChanged();
            });
        });
    }

    public void Dispose()
    {
        // Remove a inscrição ao descartar o componente
        MessagingCenter.Unsubscribe<Listar, Empresa>(SystemMessages.EmpresaSelecionada, SystemMessages.EmpresaSelecionada);
    }
}
```

### Boas Práticas

1. **Sempre Desinscrever**: Implemente `IDisposable` e desinscreva-se das mensagens no método `Dispose()`
2. **Use InvokeAsync**: Para Blazor Server, sempre use `InvokeAsync()` dentro dos callbacks para atualizações de UI
3. **Nomes Descritivos**: Use nomes descritivos para as mensagens (ex: "empresa_selecionada", "produto_atualizado")
4. **Tipagem Forte**: Sempre especifique os tipos corretos no sender e nos dados
5. **Evite Vazamentos**: Certifique-se de que componentes se desinscrevem adequadamente

### Cenários de Uso

- **Seleção de Empresa**: Comunicar mudança de empresa entre componentes
- **Atualização de Dados**: Notificar quando dados são atualizados em uma tela
- **Filtros Globais**: Aplicar filtros que afetam múltiplos componentes
- **Notificações**: Enviar notificações para componentes específicos
- **Sincronização**: Manter dados sincronizados entre diferentes partes da aplicação

### Exemplo de Implementação Completa

```csharp
// Serviço para centralizar mensagens do sistema
public static class SystemMessages
{
    public const string EmpresaSelecionada = "empresa_selecionada";
    public const string DadosAtualizados = "dados_atualizados";
    public const string FiltroAplicado = "filtro_aplicado";
}

// Uso no componente
MessagingCenter.Send<MeuComponente, Empresa>(this, SystemMessages.EmpresaSelecionada, empresa);
```

# Padronização de Rotas Blazor

**OBRIGATÓRIO: Todas as páginas Blazor devem usar as constantes de rota definidas em [Application.Routes](mdc:src/Otikka.Application/Constants/Application.cs).**

## Uso de Constantes de Rota

O projeto centraliza todas as rotas na classe `Application.Routes` localizada em [src/Otikka.Application/Constants/Application.cs](mdc:src/Otikka.Application/Constants/Application.cs). Este padrão é **obrigatório** e deve ser seguido em todas as telas.

### Padrão Obrigatório

```csharp
@attribute [Route(Application.Routes.NomeDaRota)]
```

### Exemplos de Uso

#### Tela de Cadastro de Cliente

```csharp
@attribute [Route(Application.Routes.ClienteCadastrar)]
@page "/Dashboard/Cliente/Cadastrar"
```

#### Tela de Edição de Produto

```csharp
@attribute [Route(Application.Routes.ProdutoEditar)]
@page "/Dashboard/ProdutoModulo/Produto/Editar/{id:guid}"
```

#### Tela de Listagem de Ordens de Serviço

```csharp
@attribute [Route(Application.Routes.OrdemServicoListar)]
@page "/Dashboard/OrdemServico/Listar"
```

### Estrutura das Constantes

As rotas estão organizadas por módulos na classe `Application.Routes`:

```csharp
public static class Routes
{
    // Dashboard
    public const string Dashboard = "/dashboard";

    // Autenticação
    public const string Login = "/Auth/Login";
    public const string Logout = "/Auth/Logout";

    // Cliente
    public const string ClienteListar = "/Dashboard/Cliente/Listar";
    public const string ClienteEditar = "/Dashboard/Cliente/Editar/{id:guid}";
    public const string ClienteCadastrar = "/Dashboard/Cliente/Cadastrar";

    // Produtos
    public const string ProdutoListar = "/Dashboard/ProdutoModulo/Produto/Listar";
    public const string ProdutoEditar = "/Dashboard/ProdutoModulo/Produto/Editar/{id:guid}";
    public const string ProdutoCadastrar = "/Dashboard/ProdutoModulo/Produto/Cadastrar";

    // ... outras rotas organizadas por módulo
}
```

### Rotas com Parâmetros

Para rotas que recebem parâmetros, utilize o método `GerarRota()`:

```csharp
// Navegar para edição de cliente
var rotaEdicao = Application.Routes.GerarRota(Application.Routes.ClienteEditar, clienteId.ToString());
NavigationManager.NavigateTo(rotaEdicao);

// Navegar para receita de um cliente específico
var rotaReceita = Application.Routes.GerarRota(Application.Routes.ReceitaListar, clienteId.ToString());
NavigationManager.NavigateTo(rotaReceita);
```

### Categorias de Rotas

#### 1. **Módulo Cliente**

- `ClienteListar`: Listagem de clientes
- `ClienteEditar`: Edição de cliente específico
- `ClienteCadastrar`: Cadastro de novo cliente
- `ReceitaListar`: Receitas de um cliente
- `ReceitaEditar`: Edição de receita específica
- `ReceitaCadastrar`: Cadastro de nova receita

#### 2. **Módulo Produtos**

- `ProdutoListar`: Listagem de produtos
- `ProdutoEditar`: Edição de produto específico
- `ProdutoCadastrar`: Cadastro de novo produto
- `CategoriaProdutoListar`: Listagem de categorias
- `MarcaListar`: Listagem de marcas

#### 3. **Módulo Financeiro**

- `TransacaoFinanceiraListar`: Listagem de transações
- `TransacaoFinanceiraEditar`: Edição de transação
- `TransacaoFinanceiraCadastrar`: Cadastro de transação
- `FinanceiroCategoriaListar`: Categorias financeiras

#### 4. **Módulo Ordem de Serviço**

- `OrdemServicoListar`: Listagem de ordens
- `OrdemServicoEditar`: Edição de ordem específica
- `OrdemServicoCadastrar`: Cadastro de nova ordem
- `OrdemServicoVisualizar`: Visualização de ordem

### Navegação Programática

```csharp
// Exemplo de navegação usando as constantes
public void Navegar()
{
    // Navegação simples
    NavigationManager.NavigateTo(Application.Routes.ClienteListar);

    // Navegação com parâmetro
    var clienteId = Guid.NewGuid();
    var rota = Application.Routes.GerarRota(Application.Routes.ClienteEditar, clienteId.ToString());
    NavigationManager.NavigateTo(rota);
}
```

### Benefícios do Padrão

1. **Centralização**: Todas as rotas em um local único
2. **Manutenibilidade**: Mudanças de rota atualizadas automaticamente
3. **IntelliSense**: Autocompletar e verificação de tipos
4. **Refatoração Segura**: Renomeação automática em todo o código
5. **Consistência**: Padrão uniforme em toda a aplicação
6. **Facilita Testes**: Rotas podem ser testadas facilmente

### Regras Obrigatórias

❌ **NÃO usar strings literais para rotas:**

```csharp
// NUNCA FAZER ISSO
@attribute [Route("/Dashboard/Cliente/Cadastrar")]
```

❌ **NÃO usar @page sem @attribute:**

```csharp
// NUNCA FAZER ISSO
@page "/Dashboard/Cliente/Cadastrar"
```

✅ **SEMPRE usar as constantes:**

```csharp
// SEMPRE FAZER ASSIM
@attribute [Route(Application.Routes.ClienteCadastrar)]
```

### Adicionando Novas Rotas

Quando criar uma nova página:

1. **Adicione a constante** em `Application.Routes`
2. **Use a constante** na página Blazor
3. **Siga a convenção** de nomenclatura existente
4. **Organize por módulo** usando #region

```csharp
// 1. Adicionar em Application.cs
#region NovoModulo
public const string NovoModuloListar = "/Dashboard/NovoModulo/Listar";
public const string NovoModuloEditar = "/Dashboard/NovoModulo/Editar/{id:guid}";
public const string NovoModuloCadastrar = "/Dashboard/NovoModulo/Cadastrar";
#endregion

// 2. Usar na página Blazor
@attribute [Route(Application.Routes.NovoModuloListar)]
```

# Convenções de Nomenclatura

- Use PascalCase para nomes de classes, métodos e propriedades públicas (ex.: `InfobipHelper`, `StartCallHandlingProcess`).
- Use camelCase para campos privados e variáveis ​​locais (ex.: `_callStates`, `conversationId`).
- Prefixe nomes de interface com "I" (ex.: `IZohoService`, `IElevenLabsService`).
- Use nomes descritivos para variáveis, métodos e classes que indiquem claramente sua finalidade.
- Escreva comentários de código em português.

# Estrutura do Projeto

O projeto está estruturado da seguinte forma:

```
OTIKKA/
└── src/
    ├── Otikka.Domain/ # Camada de Domínio
    ├── Otikka.Domain/Entities/ # Entidades e Modelos de Negócio
    ├── Otikka.Domain/Interfaces/ # Interfaces de modelo
    ├── Otikka.Domain/Common/ # Classes comuns e de utilidade para as outras classes do Domain.
    ├── Otikka.Domain/Enums/ # Enumeradores
    ├── Otikka.Application/ # Camada de Aplicação
    ├── Otikka.Application/Common/Extensions # Classes de extensão, geralmente validações.
    ├── Otikka.Application/Constants # Constante, aqui fica a classe Application, nela temos constantes que determinar Rotas do Blazor - Application.Routes, nome das chaves de armazenamentos locais Application.Storage (LocalStorage) e também mensagens mais populares usadas pelo sistema Application.Messages
    ├── Otikka.Application/Contracts/ # Interfaces de infraestrutura(Serviços diversos) e persistencia(Repository Pattern)
    ├── Otikka.Application/ApplicationServiceRegistration.cs # Esta classe configura a configuração DependencyInjection da biblioteca de classes
    ├── Otikka.Application/Features/ # Funcionalidades e casos de uso usando a Biblioteca WolverineFx, toda operação de banco de dados deve ser feita pelo Repositorio e chamada pela classe Handle pelo Wolverine.
    ├── Otikka.Application/Models/ # Modelos necessários para o funcionamento do Application, como modelos para consultar serviços, ou retornar dados personalizados (Request/Response) para a Tela.
    ├── Otikka.Application/Utilidades/ # Classes de utilidade para o Application e o projeto App.
    ├── Otikka.Application/Validators/ # Classes de validação dos modelos usando FluentValidation.
    ├── Otikka.Infrastructure/ # Camada de Infraestrutura, aqui temos vários serviços responsáveis por consumir APIs e SDKs.
    ├── Otikka.Infrastructure/Storage/ # Serviços de armazenamento (S3StorageService, MinioStorageService)
    ├── Otikka.Persistence/ # Camada de Persistência, aqui temos classes que implementam as interfaces de repositórios "Otikka.Application/Contracts/Persistence".
    └── Otikka.App/ # Camada de UI, aqui temos o projeto Blazor Server, que irá interagir com o usuário. Usa MessagingCenter para comunicação entre componentes.
└── tests/
    ├── Otikka.App.IntegrationTests/ # Testes de Integração para o projeto Otikka.App usando bUnit
    ├── Otikka.Persistence.IntegrationTests/ # Testes de Integração para o projeto Otikka.Persistence
    └── Otikka.Application.UnitTests/ # Testes Unitários para o projeto Otikka.Application
```

## Projeto Otikka.Domain

O projeto de domínio contendo as principais entidades, interfaces e regras de negócio.

## Projeto Otikka.Application

O projeto do aplicativo que contém a lógica de negócios e os serviços, utilizando CQRS com Wolverine .NET.

## Projeto Otikka.Infrastructure

O projeto de infraestrutura e outros serviços de infraestrutura, consumindo serviços externos como AWS S3, Stripe, Twilio, etc.

## Projeto Otikka.Persistence

O projeto de persistência que contém o contexto do banco de dados e as configurações de entidade.

## Projeto Otikka.App

O projeto de IU desenvolvido com o Blazor Server. Utiliza MessagingCenter para comunicação entre componentes.

## Projeto Otikka.App.IntegrationTests

Os testes de integração para o projeto Otikka.App.

## Projeto Otikka.Persistence.IntegrationTests

Os testes de integração para o projeto Otikka.Persistence.

## Projeto Otikka.Application.UnitTests

Os testes unitários para o projeto Otikka.Application.

# Regras de Desenvolvimento .NET

- Você é um desenvolvedor backend .NET sênior e especialista em C#, ASP.NET Core, Blazor e Entity Framework Core.

## Estilo e Estrutura do Código

- Escreva código C# conciso e idiomático com exemplos precisos.
- Siga as convenções e práticas recomendadas do .NET e ASP.NET Core.
- Use padrões de programação funcional e orientada a objetos conforme apropriado.
- Prefira expressões LINQ e lambda para operações de coleta.
- Use nomes descritivos de variáveis ​​e métodos (por exemplo, 'IsUserSignedIn', 'CalculateTotal').
- Estruture arquivos de acordo com as convenções .NET (Controladores, Modelos, Serviços, etc.).

## Uso de C# e .NET

- Use recursos do C# 10+ quando apropriado (por exemplo, tipos de registro, correspondência de padrões, atribuição de coalescência nula).
- Aproveite os recursos e middleware integrados do ASP.NET Core.

- Use o Entity Framework Core de forma eficaz para operações de banco de dados.

## Sintaxe e Formatação

- Siga as Convenções de Codificação do C# (https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions)
- Use a sintaxe expressiva do C# (por exemplo, operadores nulos-condicionais, interpolação de strings)
- Use 'var' para tipagem implícita quando o tipo for óbvio.

## Tratamento e Validação de Erros

- Use exceções para casos excepcionais, não para fluxo de controle.
- Implemente o registro de erros adequado usando o registro interno do .NET ou um registrador de terceiros.
- Use FluentValidators

## Wokflow de chamadas

- O usuário realiza uma operação na tela do projeto Otikka.App(Blazor Server) se essa operação for um preenchimento de formulário o FluentValidator irá entrar em ação e validar antes do envio para os próximos componentes, o componente instancia a classe necessária para a operação e aciona o handle usando Wolverine, o Handle receber a solicitação e se ela for uma interação com banco de dados será utilizado o repositório da respectiva operação.
- Para upload de arquivos, SEMPRE use o IFileStorageService com implementação S3StorageService para armazenar na AWS S3.
- Para comunicação entre componentes Blazor, SEMPRE use o MessagingCenter da biblioteca AKSoftware.Blazor.Utilities.
