@using Otikka.App.Components.Pages.Dashboard.EmpresaModulo.Componentes
@using Otikka.Application.Features.Empresa.Commands.CreateEmpresa
@using Otikka.Application.Features.Empresa.Queries.GetCompany
@using Otikka.Domain.Entities.UsuarioModule
@using Otikka.Application.Constants
@attribute [Route(Application.Routes.EmpresaCadastrar)]
@inherits PageBase
@inject ILogger<Otikka.App.Components.Pages.Dashboard.EmpresaModulo.Cadastrar> Logger

<PageTitle>
    Empresa - Cadastro
</PageTitle>
<EmpresaForm EhEdicao="@false" Empresa="Empresa" OnSave="Save" />

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync("applyThemeConfig");
            await JsRuntime.InvokeVoidAsync("loadApps");
        }
    }

    [SupplyParameterFromForm]
    private CreateEmpresa Empresa { get; set; } = new()
    {
        TipoNumeracaoOrdemServico = TipoAlgoritmoGeradorId.Automatico,
        TipoNumeracaoVenda = TipoAlgoritmoGeradorId.Automatico,
        Endereco = new()
    };

    private async Task Save()
    {
        try
        {
            // Campos de auditoria
            Empresa.UsuarioId = await GetUsuarioIdLoggedAsync();
            Empresa.CriadoPorId = await GetUsuarioIdLoggedAsync();
            Empresa.DataCriacao = DateTime.UtcNow;

            // Log para debugging
            Logger.LogInformation("Iniciando cadastro de empresa pelo usuário {UserId}", Empresa.UsuarioId);

            var result = await MessageBus.InvokeAsync<Result>(Empresa);

            if (result.IsSuccess)
            {
                // Empresa cadastrada com sucesso - busca a empresa criada e seleciona automaticamente
                await SelecionarEmpresaCriada();
            }
            else
            {
                // Separa erros de validação de erros de upload
                var errors = result.Errors.Select(e => e.Message).ToList();
                var validationErrors = errors.Where(e => !e.Contains("upload") && !e.Contains("logomarca")).ToList();
                var uploadErrors = errors.Where(e => e.Contains("upload") || e.Contains("logomarca")).ToList();

                if (validationErrors.Any())
                {
                    // Erros de validação impedem o cadastro
                    var errorMessage = string.Join(", ", validationErrors);
                    ErrorMessage = errorMessage;
                    return;
                }

                if (uploadErrors.Any())
                {
                    // Empresa foi cadastrada mas houve problema no upload da imagem
                    var uploadMessage = string.Join(", ", uploadErrors);
                    WarningMessage = $"Empresa cadastrada com sucesso, mas houve um problema no upload da logomarca: {uploadMessage}";

                    // Continua o fluxo normalmente
                    await SelecionarEmpresaCriada();
                    return;
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro inesperado durante o cadastro da empresa");
            ErrorMessage = $"Ocorreu um erro inesperado: {ex.Message}";
        }
    }

    private async Task SelecionarEmpresaCriada()
    {
        try
        {
            // Busca a empresa recém-criada
            var query = new GetEmpresa { Id = Empresa.Id };
            var empresaResult = await MessageBus.InvokeAsync<Result<Empresa>>(query);

            if (empresaResult.IsSuccess && empresaResult.Value != null)
            {
                // Seleciona a empresa automaticamente
                await LocalStorage.SetItemAsync<Empresa>(Application.Storage.Company, empresaResult.Value);

                SuccessMessage = Application.Messages.SucessoSalvar;

                MessagingCenter.Send(SystemMessages.EmpresaSelecionada, SystemMessages.EmpresaSelecionada, empresaResult.Value);

                // Aguarda um pouco para mostrar a mensagem e redireciona para o dashboard
                NavigationManager.NavigateTo(Application.Routes.Dashboard);
            }
            else
            {
                // Se não conseguir buscar a empresa, ainda assim mostra sucesso mas vai para seleção
                SuccessMessage = "Empresa cadastrada com sucesso!";
                NavigationManager.NavigateTo(Application.Routes.EmpresaSelecao);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erro ao selecionar empresa criada");
            SuccessMessage = "Empresa cadastrada com sucesso!";
            NavigationManager.NavigateTo(Application.Routes.EmpresaSelecao);
        }
    }
}