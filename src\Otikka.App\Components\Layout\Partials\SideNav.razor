@using Otikka.Application.Constants
@rendermode InteractiveServer
@inject IJSRuntime JsRuntime;
@inject NavigationManager NavigationManager
@inject ILocalStorageService LocalStorageService;
@implements IDisposable

<!-- Sidenav Menu Start -->
<div class="sidenav-menu">

    <!-- Brand Logo -->
    <a href="/" class="logo">
        <span class="logo-light">
            <span class="logo-lg"><img src="images/logo.png" alt="logo"></span>
            <span class="logo-sm"><img src="images/logo-sm.png" alt="small logo"></span>
        </span>

        <span class="logo-dark">
            <span class="logo-lg"><img src="images/logo-dark.png" alt="dark logo"></span>
            <span class="logo-sm"><img src="images/logo-sm.png" alt="small logo"></span>
        </span>
    </a>

    <!-- Sidebar Hover Menu Toggle Button -->
    <button class="button-sm-hover">
        <i class="ti ti-circle align-middle"></i>
    </button>

    <!-- Full Sidebar Menu Close Button -->
    <button class="button-close-fullsidebar">
        <i class="ti ti-x align-middle"></i>
    </button>

    <div data-simplebar>

        <!--- Sidenav Menu -->
        @if(empresaSelecionada is not null){
            <Menu />
        }
        else
        {        
            <ul class="side-nav" id="two-col-menu">
                <li class="side-nav-title">Menu</li>
                <li class="side-nav-item">
                    <a href="@Application.Routes.Suporte" class="side-nav-link">
                        <span class="menu-icon"><i class="ri-phone-line"></i></span>
                        <span class="menu-text">Suporte</span>
                    </a>
                </li>
            </ul>
        
            <div class="help-box text-center">
                <img src="images/coffee-cup.svg" height="90" alt="Helper Icon Image">
                <h5 class="mt-3 fw-semibold fs-16">Acesso restrito</h5>
                <p class="mb-3 text-muted">
                    Para acessar todos os recursos do sistema, cadastre uma empresa ou aguarde a aprovação do seu vínculo com uma empresa existente.
                </p>
                <a href="@Application.Routes.EmpresaCadastrar" class="btn btn-danger btn-sm">Cadastrar empresa</a>
            </div>
        }

        <div class="clearfix"></div>
    </div>
</div>
@code {
    private Empresa? empresaSelecionada;

    protected override async Task OnInitializedAsync()
    {
        // Carrega empresa selecionada do localStorage se existir
        empresaSelecionada = await LocalStorageService.GetItemAsync<Empresa>(Application.Storage.Company);
        
        // Se inscreve para receber notificações de empresa selecionada
        MessagingCenter.Subscribe<string, Empresa>(SystemMessages.EmpresaSelecionada, SystemMessages.EmpresaSelecionada, async (sender, empresa) =>
        {
            // Para Blazor Server, precisa usar InvokeAsync para atualizações de UI
            await InvokeAsync(() =>
            {
                empresaSelecionada = empresa;
                StateHasChanged();
            });
        });
    }
    public void Dispose()
    {
        // Remove a inscrição ao descartar o componente
        MessagingCenter.Unsubscribe<string, Empresa>(SystemMessages.EmpresaSelecionada, SystemMessages.EmpresaSelecionada);
    }
}