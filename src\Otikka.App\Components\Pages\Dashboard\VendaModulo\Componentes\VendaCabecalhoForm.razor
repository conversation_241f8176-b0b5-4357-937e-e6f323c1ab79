@using Otikka.App.Components.Pages.Dashboard.ClienteModulo.Cliente.Componentes
@using Otikka.Domain.Entities.PessoaModulo

@using Otikka.Application.Features.Colaborador.Queries.GetColaboradoresEmpresa
@using Otikka.Application.Features.Pessoa.Queries.ListAllClientes
@using Otikka.Application.Features.FormaPagamento.Queries.GetAllFormasPagamento
@using Otikka.Domain.Entities.VendaModulo
@using Wolverine
@inherits PageBase

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">@(EhEdicao ? "Atualizar" : "Cadastrar") venda</h4>
            </div>
            <div class="card-body">
                <div class="row gy-4 mt-0">
                    @if (Empresa is not null)
                    {
                        @if (Empresa.TipoNumeracaoVenda == TipoAlgoritmoGeradorId.Manual)
                        {
                            <div class="col-xxl-3 col-md-6">
                                <div>
                                    <label for="numero" class="form-label">Número da venda</label>
                                    <InputText @bind-Value="Venda.NumeroIdentificador" class="form-control"/>
                                    <ValidationMessage For="() => Venda!.NumeroIdentificador"/>
                                </div>
                            </div>
                        }
                    }

                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="datavenda" class="form-label">Data da venda</label>
                            <InputDate Type="InputDateType.DateTimeLocal" @bind-Value="Venda!.DataVenda" autocomplete="not" class="form-control" id="datavenda"/>
                            <ValidationMessage For="() => Venda!.DataVenda"/>
                        </div>
                    </div>

                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="cliente" class="form-label">Cliente</label>
                            <div class="input-group flex-nowrap">
                                @if (Clientes is not null)
                                {
                                    <SfComboBox CssClass="sync-adapt-select" TValue="Guid" TItem="Pessoa" @bind-Value="Venda!.ClienteId" DataSource="@Clientes" Placeholder="Escolha o cliente" FilterType="FilterType.Contains" AllowFiltering="true">
                                        <ComboBoxFieldSettings Text="Nome" Value="Id"></ComboBoxFieldSettings>
                                        <ComboBoxTemplates TItem="Pessoa">
                                            <ItemTemplate Context="pessoa">
                                                <div> @pessoa.Nome @(pessoa.Documento is not null ? $"({pessoa.Documento.FormatarCpfCnpj()})" : "")</div>
                                            </ItemTemplate>
                                        </ComboBoxTemplates>
                                    </SfComboBox>
                                }
                                <button class="btn btn-primary" type="button" @onclick="AbrirModal">+</button>
                            </div>
                            <ValidationMessage For="() => Venda!.ClienteId"/>
                        </div>
                    </div>
                    <div class="col-xxl-3 col-md-6">
                        <div>
                            <label for="vendedor" class="form-label">Vendedor</label>
                            <SfComboBox CssClass="sync-adapt-select" TValue="Guid?" TItem="Usuario" @bind-Value="Venda!.VendedorId" DataSource="@Colaboradores" Placeholder="Escolho o vendedor" FilterType="FilterType.Contains" AllowFiltering="true">
                                <ComboBoxFieldSettings Text="Nome" Value="Id"></ComboBoxFieldSettings>
                            </SfComboBox>
                            <ValidationMessage For="() => Venda!.VendedorId"/>
                        </div>
                    </div>

                    <div class="row gy-4 mt-1">
                        <div class="col-xxl-12 col-md-12">
                            <div>
                                <label for="Observation" class="form-label">Observação</label>
                                <InputTextArea @bind-Value="Venda.Observacao" class="form-control" id="Observation"></InputTextArea>
                                <ValidationMessage For="() => Venda!.Observacao"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public bool EhEdicao { get; set; }
    [Parameter] public Venda Venda { get; set; } = null!;

    private List<Usuario>? Colaboradores { get; set; }

    private List<Pessoa>? Clientes { get; set; }

    private Empresa? Empresa { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Empresa = await GetEmpresaAsync();
        var empresaId = Empresa!.Id;

        // Carregar colaboradores usando handler
        try
        {
            var colaboradoresQuery = new GetColaboradoresEmpresa { EmpresaId = empresaId, ApenasAtivos = true };
            var colaboradoresResult = await MessageBus.InvokeAsync<Result<List<Usuario>>>(colaboradoresQuery);
            
            if (colaboradoresResult.IsSuccess)
            {
                Colaboradores = colaboradoresResult.Value.Select(a => new Usuario() { Id = a.Id, Nome = $"{a.Nome} ({a.Email})" }).ToList();
            }
            else
            {
                await AlertService.ShowError("Opps!", "Erro ao carregar colaboradores");
                Colaboradores = new List<Usuario>();
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro ao carregar colaboradores: {ex.Message}");
            Colaboradores = new List<Usuario>();
        }



        // Carregar clientes usando handler
        try
        {
            var clientesQuery = new ListAllClientes { EmpresaId = empresaId };
            var clientesResult = await MessageBus.InvokeAsync<Result<List<Pessoa>>>(clientesQuery);
            
            if (clientesResult.IsSuccess)
            {
                Clientes = clientesResult.Value;
            }
            else
            {
                await AlertService.ShowError("Opps!", "Erro ao carregar clientes");
                Clientes = new List<Pessoa>();
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro ao carregar clientes: {ex.Message}");
            Clientes = new List<Pessoa>();
        }

        // Carregar formas de pagamento usando handler
        List<Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento> FormasPagamento;
        try
        {
            var formasPagamentoQuery = new GetAllFormasPagamento() { EmpresaId = Empresa.Id };
            var formasPagamentoResult = await MessageBus.InvokeAsync<Result<List<Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento>>>(formasPagamentoQuery);
            
            if (formasPagamentoResult.IsSuccess)
            {
                FormasPagamento = formasPagamentoResult.Value;
            }
            else
            {
                await AlertService.ShowError("Opps!", "Erro ao carregar formas de pagamento");
                FormasPagamento = new List<Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento>();
            }
        }
        catch (Exception ex)
        {
            await AlertService.ShowError("Opps!", $"Erro ao carregar formas de pagamento: {ex.Message}");
            FormasPagamento = new List<Otikka.Domain.Entities.TransacaoFinanceiraModulo.FormaPagamento>();
        }

        if (Colaboradores is not null && Colaboradores.Count <= 0)
        {
            await AlertService.ShowAlert("É necessário ter um colaborador cadastrado para continuar com o cadastro da ordem de serviço!");
            NavigationManager.NavigateTo(Application.Routes.ColaboradorCadastrar);

            return;
        }


        if (FormasPagamento.Count == 0)
        {
            await AlertService.ShowAlert("É necessário ter pelo menos uma forma de pagamento para continuar com o cadastro da ordem de serviço!");
            NavigationManager.NavigateTo(Application.Routes.FormaPagamentoCadastrar);

            return;
        }


        if (Colaboradores?.Count > 0) Venda.VendedorId = await GetUsuarioIdLoggedAsync();
    }

    private async Task AbrirModal()
    {
        var options = new ModalOptions() 
        { 
            Size = ModalSize.Large 
        };
        var modal = Modal.Show<ClienteFormModal>("Cliente - Cadastro rápido", options);
        var result = await modal.Result;
        
        if (result.Confirmed && result.Data is Pessoa cliente)
        {
            Clientes ??= new List<Pessoa>();
            Clientes.Add(cliente);
            Venda.ClienteId = cliente.Id;
        }
    }
}