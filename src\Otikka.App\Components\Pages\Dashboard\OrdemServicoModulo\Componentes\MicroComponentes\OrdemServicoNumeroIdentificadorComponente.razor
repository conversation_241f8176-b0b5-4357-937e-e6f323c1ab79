@using Otikka.Domain.Entities.OrdemServicoModulo
@inherits PageBase
@if (Empresa is not null && OrdemServico is not null)
{
    @if (Empresa.TipoNumeracaoOrdemServico == TipoAlgoritmoGeradorId.Automatico && !string.IsNullOrWhiteSpace(Empresa.Sigla))
    {
        <span>@Empresa.Sigla-</span>
    }
}
@if (OrdemServico is not null)
{
    @(OrdemServico.NumeroIdentificador != null ? OrdemServico.NumeroIdentificador : "")
}

@code {
    public Empresa? Empresa;
    [Parameter] public OrdemServico? OrdemServico { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Empresa = await GetEmpresaAsync();
    }

}