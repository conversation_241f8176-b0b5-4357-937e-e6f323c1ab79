using Otikka.Application.Contracts.Persistence;
using Otikka.Application.Models;
using Otikka.Domain.Entities.Common;
using Otikka.Domain.Entities.TransacaoFinanceiraModulo;

namespace Otikka.Persistence.Repositories;

public class EmpresaRepositorio : IEmpresaRepositorio
{
    private readonly IDbContextFactory<ApplicationDbContext> _factory;

    public EmpresaRepositorio(IDbContextFactory<ApplicationDbContext> factory)
    {
        _factory = factory;
    }

    public async Task<List<Empresa>> ObterEmpresas(Guid UserId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        var query = _db.Empresas
            .Where(a => a.EmpresaPaiId == null)
            .Where(a => a.UsuarioId == UserId);

        var items = await query
            .Include(a => a.Endereco)
            .ToListAsync();

        return items;
    }

    public async Task<List<Empresa>> ObterEmpresasParaLogar(Guid UserId)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        var query = _db.Empresas
            .Where(a => a.EmpresaPaiId == null)
            .Where(a => a.UsuarioId == UserId);

        var items = await query
            .Include(a => a.Endereco)
            .ToListAsync();

        var lista = await _db.UsuariosEmpresas.Where(a => a.UsuarioId == UserId).Include(a => a.Empresa).ThenInclude(a => a!.Endereco).ToListAsync();
        foreach (var item in lista)
        {
            if (item.Empresa is not null)
            {
                item.Empresa.UsuariosEmpresas = null;
                items.Add(item.Empresa);
            }
        }
        return items;
    }

    public async Task<PaginatedList<Empresa>> ObterTudo(Guid UserId, PaginationParameters paginationParams)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        var query = _db.Empresas
            .Where(a => a.EmpresaPaiId == null)
            .Where(a => a.UsuarioId == UserId);

        if (!string.IsNullOrWhiteSpace(paginationParams.SearchWord))
            query = query.Where(a => (a.NomeFantasia ?? string.Empty).Contains(paginationParams.SearchWord) || (a.RazaoSocial ?? string.Empty).Contains(paginationParams.SearchWord));

        query = query.Include(a => a.Endereco);

        // Aplicar ordenação
        query = ApplySortEmpresa(query, paginationParams.SortColumn, paginationParams.Ascending);

        var items = await query
            .Skip((paginationParams.PageIndex - 1) * paginationParams.PageSize)
            .Take(paginationParams.PageSize)
            .ToListAsync();

        var count = await _db.Empresas
            .Where(a => a.EmpresaPaiId == null)
            .Where(a => a.UsuarioId == UserId)
            .Where(a => string.IsNullOrWhiteSpace(paginationParams.SearchWord) || (a.NomeFantasia ?? string.Empty).Contains(paginationParams.SearchWord) || (a.RazaoSocial ?? string.Empty).Contains(paginationParams.SearchWord))
            .CountAsync();

        int totalPages = (int)Math.Ceiling((decimal)count / paginationParams.PageSize);

        return new PaginatedList<Empresa>(items, paginationParams.PageIndex, totalPages, paginationParams.SortColumn, paginationParams.Ascending);
    }



    public async Task<Empresa?> Obter(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        return await _db.Empresas.Include(a => a.Endereco).SingleOrDefaultAsync(a => a.Id == id);
    }

    public async Task Cadastrar(Empresa entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        await using var transaction = await _db.Database.BeginTransactionAsync();

        _db.Empresas.Add(entity);

        _db.FormasPagamento.Add(new FormaPagamento() { Nome = "Dinheiro", Prioridade = 1, EmpresaId = entity.Id });
        _db.FormasPagamento.Add(new FormaPagamento() { Nome = "PIX", Prioridade = 2, EmpresaId = entity.Id });
        _db.FormasPagamento.Add(new FormaPagamento() { Nome = "Cartão de crédito", Prioridade = 3, EmpresaId = entity.Id });
        _db.FormasPagamento.Add(new FormaPagamento() { Nome = "Cartão de débito", Prioridade = 4, EmpresaId = entity.Id });
        _db.FormasPagamento.Add(new FormaPagamento() { Nome = "Boleto bancário", Prioridade = 5, EmpresaId = entity.Id });
        _db.FormasPagamento.Add(new FormaPagamento() { Nome = "Link de pagamento", Prioridade = 6, EmpresaId = entity.Id });

        await _db.SaveChangesAsync();

        await transaction.CommitAsync();
    }

    public async Task Atualizar(Empresa entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        // Busca a entidade existente com suas relações
        var empresaExistente = await _db.Empresas
            .Include(e => e.Endereco)
            .FirstOrDefaultAsync(e => e.Id == entity.Id);
            
        if (empresaExistente == null)
        {
            throw new InvalidOperationException($"Empresa com ID {entity.Id} não encontrada para atualização.");
        }

        // Atualiza os campos da empresa
        empresaExistente.RazaoSocial = entity.RazaoSocial;
        empresaExistente.NomeFantasia = entity.NomeFantasia;
        empresaExistente.Documento = entity.Documento;
        empresaExistente.Sigla = entity.Sigla;
        empresaExistente.Email = entity.Email;
        empresaExistente.Telefone = entity.Telefone;
        empresaExistente.TipoNumeracaoOrdemServico = entity.TipoNumeracaoOrdemServico;
        empresaExistente.TipoNumeracaoVenda = entity.TipoNumeracaoVenda;
        empresaExistente.TipoUnidade = entity.TipoUnidade;
        empresaExistente.EmpresaPaiId = entity.EmpresaPaiId;
        empresaExistente.UsuarioId = entity.UsuarioId;
        empresaExistente.LogoLargura = entity.LogoLargura;
        empresaExistente.LogoAltura = entity.LogoAltura;

        // Atualiza o endereço se fornecido
        if (entity.Endereco != null)
        {
            if (empresaExistente.Endereco == null)
            {
                // Cria novo endereço
                empresaExistente.Endereco = new Endereco
                {
                    Id = Guid.NewGuid(),
                    CodigoPostal = entity.Endereco.CodigoPostal,
                    Estado = entity.Endereco.Estado,
                    Cidade = entity.Endereco.Cidade,
                    Bairro = entity.Endereco.Bairro,
                    Logradouro = entity.Endereco.Logradouro,
                    Numero = entity.Endereco.Numero,
                    Complemento = entity.Endereco.Complemento,
                    EmpresaId = entity.Id
                };
            }
            else
            {
                // Atualiza endereço existente
                empresaExistente.Endereco.CodigoPostal = entity.Endereco.CodigoPostal;
                empresaExistente.Endereco.Estado = entity.Endereco.Estado;
                empresaExistente.Endereco.Cidade = entity.Endereco.Cidade;
                empresaExistente.Endereco.Bairro = entity.Endereco.Bairro;
                empresaExistente.Endereco.Logradouro = entity.Endereco.Logradouro;
                empresaExistente.Endereco.Numero = entity.Endereco.Numero;
                empresaExistente.Endereco.Complemento = entity.Endereco.Complemento;
            }
        }

        await _db.SaveChangesAsync();
    }

    public async Task Excluir(Guid id)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        // Carrega a entidade no mesmo contexto que será usado para exclusão
        var entity = await _db.Empresas
            .Include(a => a.Endereco)
            .SingleOrDefaultAsync(a => a.Id == id);

        if (entity is not null)
        {
            _db.Empresas.Remove(entity);
            await _db.SaveChangesAsync();
        }
    }

    public async Task Excluir(Empresa entity)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        // Anexa a entidade ao contexto se ela não estiver sendo rastreada
        if (_db.Entry(entity).State == EntityState.Detached)
        {
            _db.Empresas.Attach(entity);
        }
        
        _db.Empresas.Remove(entity);
        await _db.SaveChangesAsync();
    }

    public async Task AtualizarLogoUrl(Guid empresaId, string logoUrl)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        
        var empresa = await _db.Empresas.FirstOrDefaultAsync(e => e.Id == empresaId);
        if (empresa != null)
        {
            empresa.Logo = logoUrl;
            await _db.SaveChangesAsync();
        }
    }

    public async Task<bool> DocumentoExiste(string documento, Guid? excludeId = null)
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();

        var query = _db.Empresas.Where(a => a.Documento == documento);

        if (excludeId.HasValue)
        {
            query = query.Where(a => a.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    public async Task<List<Empresa>> ObterTodas()
    {
        using ApplicationDbContext _db = _factory.CreateDbContext();
        return await _db.Empresas
            .Where(a => a.EmpresaPaiId == null) // Apenas empresas principais
            .Include(a => a.Endereco)
            .ToListAsync();
    }

    private IQueryable<Empresa> ApplySortEmpresa(IQueryable<Empresa> query, string sortColumn, bool ascending)
    {
        if (string.IsNullOrWhiteSpace(sortColumn))
        {
            return query.OrderBy(a => a.NomeFantasia);
        }

        return sortColumn.ToLower() switch
        {
            "nomefantasia" => ascending ? query.OrderBy(a => a.NomeFantasia) : query.OrderByDescending(a => a.NomeFantasia),
            "razaosocial" => ascending ? query.OrderBy(a => a.RazaoSocial) : query.OrderByDescending(a => a.RazaoSocial),
            "documento" => ascending ? query.OrderBy(a => a.Documento) : query.OrderByDescending(a => a.Documento),
            "email" => ascending ? query.OrderBy(a => a.Email) : query.OrderByDescending(a => a.Email),
            "telefone" => ascending ? query.OrderBy(a => a.Telefone) : query.OrderByDescending(a => a.Telefone),
            "datacriacao" => ascending ? query.OrderBy(a => a.DataCriacao) : query.OrderByDescending(a => a.DataCriacao),
            _ => query.OrderBy(a => a.NomeFantasia)
        };
    }
}