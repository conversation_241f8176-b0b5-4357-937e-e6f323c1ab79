// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Otikka.Persistence;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250130000000_AddTipoNumeracaoVendaToEmpresa")]
    partial class AddTipoNumeracaoVendaToEmpresa
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            // Simplified model - in a real scenario this would contain the full model
            // For manual migration purposes, this is sufficient
        }
    }
}