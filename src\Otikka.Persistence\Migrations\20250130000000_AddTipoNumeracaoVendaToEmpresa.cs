using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Otikka.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddTipoNumeracaoVendaToEmpresa : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "TipoNumeracaoVenda",
                table: "Empresas",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                comment: "Tipo de algoritmo gerador de ID para numeração de vendas (0=Automatico, 1=Manual)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Tipo<PERSON><PERSON>racaoVend<PERSON>",
                table: "Empresas");
        }
    }
}