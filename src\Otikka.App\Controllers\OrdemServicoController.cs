﻿using Microsoft.AspNetCore.Mvc;
using Otikka.Application.Contracts.Persistence;
using Otikka.App.Libraries.Utilities.Extensions;

namespace Otikka.App.Controllers;

[Microsoft.AspNetCore.Mvc.Route("[controller]")]
public class OrdemServicoController : Controller
{
    private readonly IOrdemServicoRepositorio _ordemServicoRepositorio;
    public OrdemServicoController(IOrdemServicoRepositorio ordemServicoRepositorio)
    {
        _ordemServicoRepositorio = ordemServicoRepositorio;
    }
    [HttpGet("pdf/completo/{id}")]
    public async Task<IActionResult> PDFCompleto(Guid id, [FromQuery] bool isHTML)
    {
        var serviceOrder = await _ordemServicoRepositorio.ObterCompleto(id);

        if (isHTML)
            return View("PDF", serviceOrder);

        string html = await this.RenderViewAsync("PDFCompleto", serviceOrder, false);
        return PdfService.GeneratePDF(html);
    }
    [HttpGet("pdf/cliente/{id}")]
    public async Task<IActionResult> PDFCliente(Guid id, [FromQuery] bool isHTML)
    {
        var serviceOrder = await _ordemServicoRepositorio.ObterCompleto(id);

        if (isHTML)
            return View("PDF", serviceOrder);

        string html = await this.RenderViewAsync("PDFCliente", serviceOrder, false);
        return PdfService.GeneratePDF(html);
    }
}