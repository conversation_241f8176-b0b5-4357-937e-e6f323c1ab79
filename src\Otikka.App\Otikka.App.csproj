<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>
	<PropertyGroup>
	    <NoWarn>$(NoWarn);NU1608;</NoWarn>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AKSoftware.Blazor.Utilities" Version="1.1.0" />
		<PackageReference Include="Microsoft.Build.Tasks.Core" Version="17.14.8" />

		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>

		<!-- Fix de conflito de versão do Roslyn exigido pelo EFCore.Design -->
		<PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.14.0" />
		<!-- Referências explícitas para unificar o Roslyn em 4.14.0 no runtime -->
		<PackageReference Include="Microsoft.CodeAnalysis" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Scripting" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.VisualBasic" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.VisualBasic.Workspaces" Version="4.14.0" />

		<PackageReference Include="Select.HtmlToPdf" Version="25.2.0" />

		<PackageReference Include="Serilog.Sinks.NewRelic.Logs" Version="1.3.0" />

			
		<!-- Outhers -->
		<PackageReference Include="Syncfusion.Blazor.Core" Version="30.2.7" />
		<PackageReference Include="Syncfusion.Blazor.DropDowns" Version="30.2.7" />
		<PackageReference Include="Syncfusion.Blazor.Inputs" Version="30.2.7" />
		<PackageReference Include="Syncfusion.Blazor.Themes" Version="30.2.7" />
				
		<PackageReference Include="Blazor-ApexCharts" Version="6.0.2" />
		<PackageReference Include="Blazored.Modal" Version="7.3.1" />
		<PackageReference Include="Blazored.FluentValidation" Version="2.2.0" />

		<PackageReference Include="Extensions.FluentValidation.Br" Version="1.0.2" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />

		<PackageReference Include="Select.HtmlToPdf.NetCore" Version="25.2.0" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
		<PackageReference Include="NewRelic.LogEnrichers.Serilog" Version="1.2.0" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.8" />
		<PackageReference Include="System.Formats.Nrbf" Version="9.0.8" />
		<PackageReference Include="System.Threading.AccessControl" Version="9.0.8" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\common\DevPack\Otikka.DevPack.Blazor\Otikka.DevPack.Blazor.csproj" />
	  <ProjectReference Include="..\Otikka.Application\Otikka.Application.csproj" />
	  <ProjectReference Include="..\Otikka.Infrastructure\Otikka.Infrastructure.csproj" />
	  <ProjectReference Include="..\Otikka.Persistence\Otikka.Persistence.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="wwwroot\vendor\choices.js\public\types\cypress\e2e\" />
	  <Folder Include="wwwroot\vendor\choices.js\public\types\cypress\integration\" />
	  <Folder Include="wwwroot\vendor\choices.js\public\types\cypress\plugins\" />
	  <Folder Include="wwwroot\vendor\choices.js\public\types\cypress\support\" />
	  <Folder Include="wwwroot\vendor\choices.js\public\types\src\" />
	  <Folder Include="wwwroot\vendor\choices.js\src\" />
	  <Folder Include="wwwroot\vendor\datatables.net-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-buttons-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-buttons\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-fixedcolumns-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-fixedcolumns\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-fixedheader-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-fixedheader\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-keytable-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-keytable\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-responsive-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-responsive\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-select-bs5\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net-select\types\" />
	  <Folder Include="wwwroot\vendor\datatables.net\types\" />
	  <Folder Include="wwwroot\vendor\daterangepicker\example\" />
	  <Folder Include="wwwroot\vendor\flatpickr\types\" />
	  <Folder Include="wwwroot\vendor\flatpickr\utils\" />
	  <Folder Include="wwwroot\vendor\gmaps\docs\" />
	  <Folder Include="wwwroot\vendor\gmaps\examples\" />
	  <Folder Include="wwwroot\vendor\gmaps\test\" />
	  <Folder Include="wwwroot\vendor\gridjs\src\" />
	  <Folder Include="wwwroot\vendor\jquery-datatables-checkboxes\types\" />
	  <Folder Include="wwwroot\vendor\moment\src\" />
	  <Folder Include="wwwroot\vendor\rater-js\example\" />
	  <Folder Include="wwwroot\vendor\rater-js\test\" />
	  <Folder Include="wwwroot\vendor\swiper\types\modules\" />
	</ItemGroup>

</Project>
