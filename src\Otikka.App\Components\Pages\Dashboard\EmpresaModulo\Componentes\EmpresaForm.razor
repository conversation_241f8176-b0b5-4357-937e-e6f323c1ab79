﻿@using Otikka.App.Components.Generics
@using Otikka.Application.Contracts.Infrastructure
@using Otikka.Application.Features.Empresa.Commands.CreateEmpresa
@using Otikka.Application.Features.Empresa.Commands.UpdateEmpresa
@using Otikka.Domain.Entities.UsuarioModule
@inject ICnpjService CnpjService
@inherits PageBase

<EditForm Model="Empresa" OnValidSubmit="Submit" FormName="Register">
    <FluentValidationValidator Options="@(options => options.IncludeRuleSets("EmpresaValidacao"))"/>
    <!-- <PERSON><PERSON><PERSON><PERSON> da página -->
    <div class="page-title-head d-flex align-items-sm-center flex-sm-row flex-column gap-2">
        <div class="flex-grow-1">
            <h4 class="fs-18 fw-semibold mb-0">@(EhEdicao ? "Atualizar" : "Cadastrar") Empresa</h4>
        </div>
        <div class="text-end">
            <ol class="breadcrumb m-0 py-0">
                <li class="breadcrumb-item"><a href="@Application.Routes.Dashboard">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="@Application.Routes.EmpresaListar">Empresas</a></li>
                <li class="breadcrumb-item active">@(EhEdicao ? "Editar" : "Cadastrar")</li>
            </ol>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-7 col-lg-7">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                        <i class="ri-building-line me-2"></i>Dados da Empresa
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-md-12">
                            <label for="cnpj" class="form-label">CNPJ <span class="text-danger">*</span></label>
                            @if (EhEdicao == false)
                            {
                                <div class="input-group">
                                    <SfMaskedTextBox @bind-Value="EmpresaBase!.Documento"
                                        Mask="00.000.000/0000-00" 
                                        CssClass="input-group-masked" 
                                        id="cnpj" 
                                        placeholder="00.000.000/0000-00" 
                                        EnableLiterals="true"
                                        TValue="string" 
                                        />
                                    <button type="button" class="btn btn-primary" @onclick="BuscarCnpj">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            }
                            else
                            {
                                <InputText @bind-Value="EmpresaBase!.Documento" type="text" autocomplete="not"
                                class="form-control bg-light" id="cnpj" readonly />
                            }
                            <ValidationMessage For="() => EmpresaBase!.Documento"/>
                            @if (LoadingCnpj)
                            {
                                <small class="text-info">
                                    <i class="ri-loader-4-line spin me-1"></i>Consultando CNPJ...
                                </small>
                            }
                        </div>

                        <div class="col-md-12">
                            <label for="razaosocial" class="form-label">Razão Social <span class="text-danger">*</span></label>
                            <InputText @bind-Value="EmpresaBase!.RazaoSocial" type="text" autocomplete="not"
                            class="form-control" id="razaosocial" placeholder="Digite a razão social"/>
                            <ValidationMessage For="() => EmpresaBase!.RazaoSocial" />
                        </div>

                        <div class="col-md-12">
                            <label for="nomefantasia" class="form-label">Nome Fantasia <span class="text-danger">*</span></label>
                            <InputText @bind-Value="EmpresaBase!.NomeFantasia" type="text" autocomplete="not"
                            class="form-control" id="nomefantasia" placeholder="Digite o nome fantasia"/>
                            <ValidationMessage For="() => EmpresaBase!.NomeFantasia"/>
                        </div>

                        <div class="col-md-12">
                            <label for="sigla" class="form-label">Sigla da Empresa</label>
                            <InputText @bind-Value="EmpresaBase!.Sigla" type="text" autocomplete="not"
                            class="form-control" id="sigla" placeholder="Ex: ABC"/>
                            <ValidationMessage For="() => EmpresaBase!.Sigla" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                        <i class="ri-map-pin-line me-2"></i>Endereço
                    </h4>
                </div>
                <div class="card-body">
                    <Otikka.App.Components.Generics.EnderecoForm Endereco="EmpresaBase.Endereco" AoProcessar="AoProcessarCep" />
                </div>
            </div>
            
        </div>
        
        <!-- Card 1: Upload da Logomarca (Superior Esquerda) -->
        <div class="col-xl-5 col-lg-5">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                        <i class="ri-image-line me-2"></i>Logomarca da Empresa
                    </h4>
                </div>
                <div class="card-body">
                    <Otikka.App.Components.Generics.LogomarcaUploadForm Empresa="EmpresaBase" OnUploaded="(newFile) => { file = newFile; }" />
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="ri-information-line me-1"></i>
                            Formatos aceitos: JPG, PNG. Tamanho máximo: 10MB
                        </small>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">
                        <i class="ri-contacts-line me-2"></i>Contato e Configurações
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row gy-3">
                        <div class="col-md-12">
                            <label for="email" class="form-label">E-mail</label>
                            <InputText @bind-value="EmpresaBase!.Email" type="email" autocomplete="email"
                            class="form-control" id="email" placeholder="<EMAIL>"/>
                            <ValidationMessage For="() => EmpresaBase!.Email"/>
                        </div>

                        <div class="col-md-12">
                            <label for="telefone" class="form-label">Telefone</label>
                            <SfMaskedTextBox @bind-value="EmpresaBase!.Telefone" Mask="(00) 00000-0000" 
                            class="form-control" id="telefone" placeholder="(11) 99999-9999"/>
                            <ValidationMessage For="() => EmpresaBase!.Telefone"/>
                        </div>

                        <div class="col-md-12">
                            @if (EhEdicao == false)
                            {
                                <label for="configurarNumeroSequencia" class="form-label">Numeração de O.S.</label>
                                <InputSelect class="form-select" @bind-value="EmpresaBase!.TipoNumeracaoOrdemServico" id="configurarNumeroSequencia">
                                    @foreach (TipoAlgoritmoGeradorId simbolo in Enum.GetValues(typeof(TipoAlgoritmoGeradorId)))
                                    {
                                        <option value="@simbolo">@simbolo.GetDisplayName()</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="() => EmpresaBase!.TipoNumeracaoOrdemServico" />
                                <small class="text-muted">Como será gerado o número das Ordens de Serviço</small>
                            }
                            else
                            {
                                <label class="form-label">Numeração de O.S.</label>
                                <div class="form-control-plaintext">
                                    @EmpresaBase!.TipoNumeracaoOrdemServico.GetDisplayName()
                                </div>
                            }
                        </div>

                        <div class="col-md-12">
                            @if (EhEdicao == false)
                            {
                                <label for="configurarNumeroVenda" class="form-label">Numeração de Vendas</label>
                                <InputSelect class="form-select" @bind-value="EmpresaBase!.TipoNumeracaoVenda" id="configurarNumeroVenda">
                                    @foreach (TipoAlgoritmoGeradorId simbolo in Enum.GetValues(typeof(TipoAlgoritmoGeradorId)))
                                    {
                                        <option value="@simbolo">@simbolo.GetDisplayName()</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="() => EmpresaBase!.TipoNumeracaoVenda" />
                                <small class="text-muted">Como será gerado o número das Vendas</small>
                            }
                            else
                            {
                                <label class="form-label">Numeração de Vendas</label>
                                <div class="form-control-plaintext">
                                    @EmpresaBase!.TipoNumeracaoVenda.GetDisplayName()
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
        

        

        
    </div>

    <!-- Botões de Ação -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="@Application.Routes.EmpresaListar" class="btn btn-light">
                            <i class="ri-arrow-left-line me-2"></i>Voltar
                        </a>
                        <SubmitButton IsProcessing="Processing" ButtonClass="btn-primary">
                            <i class="ri-save-line me-2"></i>@(EhEdicao ? "Atualizar" : "Cadastrar")
                        </SubmitButton>
                    </div>
                </div>
            </div>
        </div>
    </div>
</EditForm>

@code {
    [Parameter] public object? Empresa { get; set; } = new CreateEmpresa();
    [Parameter] public bool EhEdicao { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }

    private IBrowserFile? file;
    private bool LoadingCnpj { get; set; } = false;

    // Propriedade auxiliar para acessar a empresa como Empresa base
    private Empresa? EmpresaBase => Empresa as Empresa;

    private void AoProcessarCep(bool processando)
    {
        Processing = processando;
    }

    /// <summary>
    /// Método chamado ao clicar no botão de busca para consultar dados da empresa pelo CNPJ
    /// </summary>
    private async Task BuscarCnpj()
    {
        var value = EmpresaBase?.Documento;
        if (string.IsNullOrWhiteSpace(value) || EmpresaBase is null) return;

        try
        {
            LoadingCnpj = true;
            Processing = true;

            // Verifica se o CNPJ está completo (14 dígitos)
            var cnpjLimpo = value.Replace(".", "").Replace("/", "").Replace("-", "");
            if (cnpjLimpo.Length == 14)
            {
                var companyInfo = await CnpjService.BuscarPorCnpjAsync(value);

                if (companyInfo == null)
                {
                    await ToastService.ShowWarning("O CNPJ não encontrado. Pode prosseguir, só confirme se está correto.");
                    EmpresaBase.RazaoSocial = String.Empty;
                    EmpresaBase.NomeFantasia = String.Empty;
                    EmpresaBase.Email = String.Empty;
                    EmpresaBase.Telefone = String.Empty;
                    return;
                }

                if (companyInfo.SituacaoCadastral == "OK")
                {
                    // Preenche os dados da empresa
                    EmpresaBase.RazaoSocial = companyInfo.RazaoSocial;
                    EmpresaBase.NomeFantasia = companyInfo.NomeFantasia ?? string.Empty;
                    EmpresaBase.Email = companyInfo.Email;
                    EmpresaBase.Telefone = companyInfo.Telefone;

                    // Preenche os dados do endereço se o objeto existir
                    if (EmpresaBase.Endereco != null && !string.IsNullOrWhiteSpace(companyInfo.Cep))
                    {
                        EmpresaBase.Endereco.CodigoPostal = companyInfo.Cep;
                        EmpresaBase.Endereco.Estado = companyInfo.Uf ?? string.Empty;
                        EmpresaBase.Endereco.Cidade = companyInfo.Cidade ?? string.Empty;
                        EmpresaBase.Endereco.Bairro = companyInfo.Bairro ?? string.Empty;
                        EmpresaBase.Endereco.Logradouro = companyInfo.Logradouro ?? string.Empty;
                        EmpresaBase.Endereco.Numero = companyInfo.Numero ?? string.Empty;
                        EmpresaBase.Endereco.Complemento = companyInfo.Complemento ?? string.Empty;
                    }

                    // Força a atualização da interface
                    StateHasChanged();
                }
                else
                {
                    // Limpa os campos se não encontrou a empresa
                    await ToastService.ShowFailed("A situação cadastral desse CNPJ não é valido!");
                }
            }
        }
        catch (Exception ex)
        {
            await ToastService.ShowInfo("Não encontramos o CNPJ, preencha manualmente as informações!");
        }
        finally
        {
            LoadingCnpj = false;
            Processing = false;
        }
    }

    private async Task Submit()
    {
        //TODO - Adicionar Mensagem
        if (EmpresaBase is null) return;
        try
        {
            await ProcessingChange(true);
            if (file is not null)
            {
                //TODO - Adicionar mensagem de erro caso o arquivo seja maior que 10MB
                //TODO - Pensar no tamanho máximo para os arquivos
                using var stream = file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024); // 10MB
                using var ms = new MemoryStream();
                await stream.CopyToAsync(ms);
                
                // Define a propriedade Logo (byte[]) tanto para CreateEmpresa quanto UpdateEmpresa
                if (Empresa is CreateEmpresa createCompany)
                {
                    createCompany.Logo = ms.ToArray();
                }
                else if (Empresa is UpdateEmpresa updateCompany)
                {
                    updateCompany.Logo = ms.ToArray();
                }
            }
            await OnSave.InvokeAsync();
        }
        catch (Exception e)
        {
            await JsRuntime.InvokeVoidAsync("alert", $"Ocorreu um erro! {e.Message}");
        }
        finally
        {
            await ProcessingChange(false);
        }
    }
}
