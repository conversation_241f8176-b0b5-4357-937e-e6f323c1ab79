﻿@attribute [Route(Application.Routes.Dashboard)]
@inherits PageBase

@code {
    /// <summary>
    /// Redireciona automaticamente para a listagem de ordens de serviço quando a tela é carregada
    /// </summary>
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        // Redireciona para a listagem de ordens de serviço
        NavigationManager.NavigateTo(Application.Routes.OrdemServicoListar);
    }
}
